using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Windows.Forms;

namespace ItemTableReader;

public class FormWeapons : Form
{
	private List<KeyValuePair<int, string>> nicknames = new List<KeyValuePair<int, string>>();

	private static ItemWeapon tempWeapon = new ItemWeapon();

	private static ItemWeapon tempWeapon2 = new ItemWeapon();

	private IContainer components;

	private ComboBox comboBoxWep;

	private BindingSource effectBindingSource;

	private BinaryEditor be = new BinaryEditor();

	private BindingSource itemWeaponBindingSource;

	private ListBox listBoxWeap;

	private TabPage tabPage2;

	private TableLayoutPanel tableLayoutPanel1;

	private CheckBox checkBoxCantNPC;

	private CheckBox checkBoxCantTrade;

	private CheckBox checkBoxCantStorage;

	private CheckBox checkBoxStatsTrade;

	private TabPage tabPage1;

	private DataGridView dataGridView1;

	private DataGridViewTextBoxColumn iDDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn nameDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn descriptionDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn valueDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn probDataGridViewTextBoxColumn;

	private TabPage tabPageEffects;

	private ListBox listBoxEffects;

	private TabPage tabPageUnknownBytes;

	private ByteViewer byteViewer;

	private TabPage tabRequirements;

	private TableLayoutPanel tableLayoutPanel3;

	private Label labelMainStat;

	private Label labelSecondaryStat;

	private TextBox textBoxSecondaryStat;

	private TextBox textBoxMainStat;

	private Label labelLevel;

	private TextBox textBoxLevel;

	private Label labelTextLevel;

	private TextBox textBox1;

	private TabPage tabDetails;

	private TableLayoutPanel tableLayoutPanel2;

	private Label labelMinDmg;

	private Label labelMaxDmg;

	private Label labelDurability;

	private Label labelBalance;

	private Label labelAR;

	private Label labelCrit;

	private TextBox textBoxMinDmg;

	private TextBox textBoxMaxDmg;

	private TextBox textBoxDurability;

	private TextBox textBoxBalance;

	private TextBox textBoxAR;

	private TextBox textBoxCrit;

	private TextBox textBoxHardness;

	private Label labelHardness;

	private Label labelTears;

	private TextBox textBoxTears;

	private Label labelPrice;

	private TextBox textBoxPrice;

	private Label labelSockets;

	private TextBox textBoxSockets;

	private Label labelRank;

	private TextBox textBoxRank;

	private Label labelModel;

	private Label labelIcon;

	private TextBox textBoxModel;

	private TextBox textBoxIcon;

	private Label labelTime;

	private TextBox textBoxTime;

	private Label labelContribClan;

	private Label labelContribOthers;

	private TextBox textBoxContribClan;

	private TextBox textBoxContribOthers;

	private Label labelLvlRed;

	private TextBox textBoxLvlRed;

	private Label label2;

	private Label labelCashCheck;

	private Label labelMaxSockets;

	private TextBox textBoxMaxSlots;

	private TextBox textBoxCashCheck;

	private TabPage tabBasicInfo;

	private TableLayoutPanel tableLayoutPanel4;

	private Label labelName;

	private Label labelWeaponType;

	private TextBox textBoxName;

	private ComboBox comboBoxWeaponType;

	private ComboBox comboBoxWeaponThirdType;

	private Label label1;

	private Button buttonLoadIcon;

	private Label labelIco;

	private Label labelXSDindex;

	private TextBox textBoxXSDindex;

	private Label label3;

	private Label label4;

	private ComboBox comboBoxNickname;

	private ComboBox comboBoxAddTo;

	private Button buttonCopy;

	private TabControl tabControl1;

	private Label label5;

	private ComboBox unknownByteIndex;

	private TextBox unknownByteValue;

	private Button button1;

	private TextBox multipleItems;

	private Button btn_copyItem;

	private Button btn_pasteItem;

	public FormWeapons()
	{
		InitializeComponent();
		for (int i = 0; i < 41; i++)
		{
			unknownByteIndex.Items.Add(i.ToString());
		}
	}

	private void textBoxLevel_TextChanged(object sender, EventArgs e)
	{
		try
		{
			labelTextLevel.Text = Level.Get(Convert.ToInt16(textBoxLevel.Text));
		}
		catch
		{
		}
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).level = Convert.ToByte(textBoxLevel.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void comboBoxWep_SelectedIndexChanged(object sender, EventArgs e)
	{
		listBoxWeap.Items.Clear();
		Dictionary<short, ItemWeapon> dictionary = null;
		dictionary = comboBoxWep.SelectedIndex switch
		{
			0 => ItemWeapon.Weapons,
			1 => ItemWeapon.Weapons2,
			2 => ItemWeapon.Weapons3,
			_ => ItemWeapon.Weapons,
		};
		listBoxWeap.BeginUpdate();
		foreach (KeyValuePair<short, ItemWeapon> item in dictionary)
		{
			listBoxWeap.DisplayMember = "FullName";
			listBoxWeap.ValueMember = "ID";
			listBoxWeap.Items.Add(item.Value);
		}
		listBoxWeap.EndUpdate();
	}

	private void buttonCopy_Click(object sender, EventArgs e)
	{
		if (listBoxWeap.SelectedItem != null)
		{
			listBoxWeap.Items.Add(new ItemWeapon(listBoxWeap.SelectedItem as ItemWeapon));
		}
	}

	private void dataGridView1_CellValueChanged(object sender, DataGridViewCellEventArgs e)
	{
		if (listBoxWeap.SelectedItem is ItemWeapon itemWeapon)
		{
			try
			{
				int key = Convert.ToInt32(dataGridView1[0, e.RowIndex].Value.ToString());
				itemWeapon.effects[e.RowIndex].Desc = XsdManager.EffectsInfo[key];
				itemWeapon.effects[e.RowIndex].Name = XsdManager.EffectsName[key];
			}
			catch
			{
			}
			dataGridView1.Refresh();
		}
	}

	private void tabBasicInfo_Click(object sender, EventArgs e)
	{
	}

	private void FormWeapons_KeyDown(object sender, KeyEventArgs e)
	{
		if (e.KeyCode == Keys.Up)
		{
			if (listBoxWeap.SelectedIndex > 0)
			{
				listBoxWeap.SelectedIndex--;
			}
		}
		else if (e.KeyCode == Keys.Down && listBoxWeap.SelectedIndex < listBoxWeap.Items.Count - 1)
		{
			listBoxWeap.SelectedIndex++;
		}
	}

	private void listBoxWeap_SelectedIndexChanged_1(object sender, EventArgs e)
	{
		ItemWeapon weapon = listBoxWeap.SelectedItem as ItemWeapon;
		comboBoxAddTo.SelectedIndex = weapon.xsdAddTo;
		comboBoxNickname.SelectedIndex = nicknames.FindIndex((KeyValuePair<int, string> el) => el.Key == weapon.xsdNick);
		textBoxXSDindex.Text = weapon.xsdName.ToString();
		dataGridView1.DataSource = weapon.effects;
		textBoxName.Text = weapon.Name;
		textBoxLevel.Text = weapon.level.ToString();
		textBoxMainStat.Text = weapon.mainStat.ToString();
		textBoxSecondaryStat.Text = weapon.secondaryStat.ToString();
		labelTextLevel.Text = Level.Get(weapon.level);
		textBoxMinDmg.Text = weapon.minDmg.ToString();
		textBoxMaxDmg.Text = weapon.maxDmg.ToString();
		textBoxDurability.Text = weapon.durabiliy.ToString();
		textBoxBalance.Text = weapon.balance.ToString();
		textBoxAR.Text = weapon.attackRate.ToString();
		textBoxCrit.Text = weapon.critRate.ToString();
		textBoxHardness.Text = weapon.hardness.ToString();
		textBoxTears.Text = "?";
		textBoxPrice.Text = weapon.Price.ToString();
		textBoxSockets.Text = weapon.sockets.ToString();
		textBoxRank.Text = weapon.rank.ToString();
		textBoxModel.Text = weapon.ModelIndex.ToString();
		textBoxIcon.Text = weapon.IconIndex.ToString();
		textBoxLvlRed.Text = weapon.reduceLevel.ToString();
		textBoxContribClan.Text = weapon.ClanPoint1.ToString();
		textBoxContribOthers.Text = weapon.ClanPoint2.ToString();
		textBoxTime.Text = weapon.Time.ToString();
		textBoxMaxSlots.Text = weapon.maxSlots.ToString();
		textBoxCashCheck.Text = weapon.CashCheck.ToString();
		checkBoxCantNPC.Checked = weapon.BlockNpcSell;
		checkBoxCantTrade.Checked = Convert.ToBoolean(weapon.BlockTrade);
		checkBoxCantStorage.Checked = weapon.BlockStorage;
		checkBoxStatsTrade.Checked = weapon.statTrade;
		buttonLoadIcon.Text = "";
		buttonLoadIcon.ImageAlign = ContentAlignment.MiddleCenter;
		buttonLoadIcon.Width = 32;
		buttonLoadIcon.Height = 32;
		buttonLoadIcon.Image = ItemParser.SetIcon(weapon);
		listBoxEffects.Items.Clear();
		Effect[] effects = weapon.effects;
		foreach (Effect item in effects)
		{
			listBoxEffects.Items.Add(item);
			listBoxEffects.ValueMember = "ID";
			listBoxEffects.DisplayMember = "Description";
		}
		textBox1.Text = "";
		textBox1.Multiline = true;
		VectorPos[] vecItemPosition = weapon.vecItemPosition;
		foreach (VectorPos vectorPos in vecItemPosition)
		{
			textBox1.Text += $"x: {vectorPos.x}\ty: {vectorPos.y}\tz: {vectorPos.z}\r\n";
		}
		byteViewer.SetBytes(weapon.unknownBytes.ToArray());
		WeaponSecondType secondType = (WeaponSecondType)weapon.SecondType;
		comboBoxWeaponType.SelectedIndex = comboBoxWeaponType.FindStringExact(secondType.ToString());
		comboBoxWeaponThirdType.Items.Clear();
		Type typeFromHandle = typeof(ThirdTypeWeaponShort);
		switch (secondType)
		{
		case WeaponSecondType.SHORT:
			typeFromHandle = typeof(ThirdTypeWeaponShort);
			break;
		case WeaponSecondType.LONG:
			typeFromHandle = typeof(ThirdTypeWeaponLong);
			break;
		case WeaponSecondType.SOFT:
			typeFromHandle = typeof(ThirdTypeWeaponSoft);
			break;
		case WeaponSecondType.HIDDEN:
			typeFromHandle = typeof(ThirdTypeWeaponHidden);
			break;
		case WeaponSecondType.MUSICAL:
			typeFromHandle = typeof(ThirdTypeWeaponMusical);
			break;
		case WeaponSecondType.SPECIAL:
			typeFromHandle = typeof(ThirdTypeWeaponSpecial);
			break;
		}
		foreach (object value in Enum.GetValues(typeFromHandle))
		{
			comboBoxWeaponThirdType.Items.Add(value);
		}
		if (unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < weapon.unknownBytes.Count)
		{
			unknownByteValue.Text = weapon.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
		}
		comboBoxWeaponThirdType.SelectedIndex = weapon.thirdType;
	}

	private void FormWeapons_Load(object sender, EventArgs e)
	{
		comboBoxWep.SelectedIndex = 0;
		comboBoxAddTo.DisplayMember = "Value";
		comboBoxAddTo.ValueMember = "Key";
		foreach (KeyValuePair<int, string> addTo in XsdManager.AddTos)
		{
			comboBoxAddTo.Items.Add(addTo);
		}
		foreach (KeyValuePair<int, string> nickname in XsdManager.Nicknames)
		{
			nicknames.Add(nickname);
		}
		comboBoxNickname.DisplayMember = "Value";
		comboBoxNickname.ValueMember = "Key";
		comboBoxNickname.DataSource = nicknames;
	}

	private void textBoxMinDmg_Leave(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).minDmg = Convert.ToInt16(textBoxMinDmg.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxMaxDmg_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).maxDmg = Convert.ToInt16(textBoxMaxDmg.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxDurability_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).durabiliy = Convert.ToUInt16(textBoxDurability.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxModel_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).ModelIndex = Convert.ToInt16(textBoxModel.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxSockets_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).sockets = Convert.ToByte(textBoxSockets.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxMaxSlots_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).maxSlots = Convert.ToByte(textBoxMaxSlots.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxMainStat_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).mainStat = Convert.ToInt16(textBoxMainStat.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxSecondaryStat_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).secondaryStat = Convert.ToInt16(textBoxSecondaryStat.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxBalance_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).balance = Convert.ToSByte(textBoxBalance.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxAR_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).attackRate = Convert.ToSByte(textBoxAR.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxCrit_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).critRate = Convert.ToInt16(textBoxCrit.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxHardness_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).hardness = Convert.ToSByte(textBoxHardness.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxPrice_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).Price = Convert.ToUInt32(textBoxPrice.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxRank_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).rank = Convert.ToSByte(textBoxRank.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxIcon_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemWeapon weapon = listBoxWeap.SelectedItem as ItemWeapon;
			if (weapon != null)
			{
				weapon.IconIndex = Convert.ToInt16(textBoxIcon.Text);

				// 更新图标显示
				try
				{
					buttonLoadIcon.Image = ItemParser.SetIcon(weapon);
					buttonLoadIcon.BackColor = Color.Transparent;
				}
				catch (Exception iconEx)
				{
					System.Diagnostics.Debug.WriteLine($"Error updating weapon icon: {iconEx.Message}");
					buttonLoadIcon.Image = null;
					buttonLoadIcon.BackColor = Color.Red;
				}
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxTime_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).Time = Convert.ToInt16(textBoxTime.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxContribClan_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).ClanPoint1 = Convert.ToInt32(textBoxContribClan.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxContribOthers_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).ClanPoint2 = Convert.ToInt32(textBoxContribOthers.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxLvlRed_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).reduceLevel = Convert.ToByte(textBoxLvlRed.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void checkBoxCantNPC_CheckedChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).BlockNpcSell = checkBoxCantNPC.Checked;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void checkBoxCantStorage_CheckedChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).BlockStorage = checkBoxCantStorage.Checked;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void checkBoxCantTrade_CheckedChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).BlockTrade = Convert.ToByte(checkBoxCantTrade.Checked);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void checkBoxStatsTrade_CheckedChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxWeap.SelectedItem as ItemWeapon).statTrade = checkBoxStatsTrade.Checked;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void comboBoxNickname_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxWeap.SelectedItem is ItemWeapon itemWeapon)
			{
				itemWeapon.xsdNick = (ushort)nicknames[comboBoxNickname.SelectedIndex].Key;
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteIndex_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxWeap.SelectedItem is ItemWeapon itemWeapon && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemWeapon.unknownBytes.Count)
			{
				unknownByteValue.Text = itemWeapon.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteValue_TextChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxWeap.SelectedItem is ItemWeapon itemWeapon && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemWeapon.unknownBytes.Count)
			{
				itemWeapon.unknownBytes[unknownByteIndex.SelectedIndex] = Convert.ToByte(unknownByteValue.Text);
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void copyEffects()
	{
		try
		{
			if (listBoxWeap.SelectedItem is ItemWeapon itemWeapon)
			{
				for (int i = 0; i < 5; i++)
				{
					tempWeapon.effects[i] = itemWeapon.effects[i];
				}
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void pasteEffects(ItemWeapon weapon)
	{
		if (weapon != null)
		{
			for (int i = 0; i < 5; i++)
			{
				weapon.effects[i].ID = tempWeapon.effects[i].ID;
				weapon.effects[i].Type = tempWeapon.effects[i].Type;
				weapon.effects[i].Value = tempWeapon.effects[i].Value;
				weapon.effects[i].Per = tempWeapon.effects[i].Per;
				weapon.effects[i].Prob = tempWeapon.effects[i].Prob;
			}
		}
	}

	private void button1_Click(object sender, EventArgs e)
	{
		copyEffects();
		try
		{
			string[] array = multipleItems.Text.Split();
			for (int i = 0; i < array.Length; i++)
			{
				short key = short.Parse(array[i]);
				if (ItemWeapon.Weapons.ContainsKey(key))
				{
					ItemWeapon weapon = ItemWeapon.Weapons[key];
					pasteEffects(weapon);
				}
				if (ItemWeapon.Weapons2.ContainsKey(key))
				{
					ItemWeapon weapon2 = ItemWeapon.Weapons2[key];
					pasteEffects(weapon2);
				}
				if (ItemWeapon.Weapons3.ContainsKey(key))
				{
					ItemWeapon weapon3 = ItemWeapon.Weapons3[key];
					pasteEffects(weapon3);
				}
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_copyItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (!(listBoxWeap.SelectedItem is ItemWeapon itemWeapon))
			{
				return;
			}
			tempWeapon2.Type = itemWeapon.Type;
			tempWeapon2.SecondType = itemWeapon.SecondType;
			tempWeapon2.xsdName = itemWeapon.xsdName;
			tempWeapon2.xsdAddTo = itemWeapon.xsdAddTo;
			tempWeapon2.xsdNick = itemWeapon.xsdNick;
			tempWeapon2.thirdType = itemWeapon.thirdType;
			tempWeapon2.XsdItemInfo = itemWeapon.XsdItemInfo;
			tempWeapon2.minDmg = itemWeapon.minDmg;
			tempWeapon2.maxDmg = itemWeapon.maxDmg;
			tempWeapon2.durabiliy = itemWeapon.durabiliy;
			tempWeapon2.balance = itemWeapon.balance;
			tempWeapon2.attackRate = itemWeapon.attackRate;
			tempWeapon2.critRate = itemWeapon.critRate;
			tempWeapon2.hardness = itemWeapon.hardness;
			tempWeapon2.level = itemWeapon.level;
			tempWeapon2.mainStat = itemWeapon.mainStat;
			tempWeapon2.secondaryStat = itemWeapon.secondaryStat;
			tempWeapon2.ApplyClan = itemWeapon.ApplyClan;
			tempWeapon2.ClanPoint1 = itemWeapon.ClanPoint1;
			tempWeapon2.ClanPoint2 = itemWeapon.ClanPoint2;
			tempWeapon2.Price = itemWeapon.Price;
			tempWeapon2.rank = itemWeapon.rank;
			tempWeapon2.hiddenID = itemWeapon.hiddenID;
			tempWeapon2.sockets = itemWeapon.sockets;
			tempWeapon2.ModelIndex = itemWeapon.ModelIndex;
			tempWeapon2.IconIndex = itemWeapon.IconIndex;
			tempWeapon2.Grade = itemWeapon.Grade;
			tempWeapon2.BlockDrop = itemWeapon.BlockDrop;
			tempWeapon2.BlockTrade = itemWeapon.BlockTrade;
			tempWeapon2.BlockNpcSell = itemWeapon.BlockNpcSell;
			tempWeapon2.CashCheck = itemWeapon.CashCheck;
			tempWeapon2.Time = itemWeapon.Time;
			tempWeapon2.BlockStorage = itemWeapon.BlockStorage;
			tempWeapon2.maxSlots = itemWeapon.maxSlots;
			tempWeapon2.reduceLevel = itemWeapon.reduceLevel;
			for (int i = 0; i < 5; i++)
			{
				tempWeapon2.effects[i] = itemWeapon.effects[i];
			}
			for (int j = 0; j < 4; j++)
			{
				tempWeapon2.specials[j] = itemWeapon.specials[j];
			}
			for (int k = 0; k < 4; k++)
			{
				tempWeapon2.vecItemPosition[k] = itemWeapon.vecItemPosition[k];
			}
			if (tempWeapon2.unknownBytes.Count == 0)
			{
				byte item = 0;
				for (int l = 0; l < itemWeapon.unknownBytes.Count; l++)
				{
					tempWeapon2.unknownBytes.Add(item);
				}
			}
			for (int m = 0; m < itemWeapon.unknownBytes.Count; m++)
			{
				tempWeapon2.unknownBytes[m] = itemWeapon.unknownBytes[m];
			}
			btn_pasteItem.Enabled = true;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btn_pasteItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxWeap.SelectedItem is ItemWeapon itemWeapon)
			{
				itemWeapon.Type = tempWeapon2.Type;
				itemWeapon.SecondType = tempWeapon2.SecondType;
				itemWeapon.xsdName = tempWeapon2.xsdName;
				itemWeapon.xsdAddTo = tempWeapon2.xsdAddTo;
				itemWeapon.xsdNick = tempWeapon2.xsdNick;
				itemWeapon.thirdType = tempWeapon2.thirdType;
				itemWeapon.XsdItemInfo = tempWeapon2.XsdItemInfo;
				itemWeapon.minDmg = tempWeapon2.minDmg;
				itemWeapon.maxDmg = tempWeapon2.maxDmg;
				itemWeapon.durabiliy = tempWeapon2.durabiliy;
				itemWeapon.balance = tempWeapon2.balance;
				itemWeapon.attackRate = tempWeapon2.attackRate;
				itemWeapon.critRate = tempWeapon2.critRate;
				itemWeapon.hardness = tempWeapon2.hardness;
				itemWeapon.level = tempWeapon2.level;
				itemWeapon.mainStat = tempWeapon2.mainStat;
				itemWeapon.secondaryStat = tempWeapon2.secondaryStat;
				itemWeapon.ApplyClan = tempWeapon2.ApplyClan;
				itemWeapon.ClanPoint1 = tempWeapon2.ClanPoint1;
				itemWeapon.ClanPoint2 = tempWeapon2.ClanPoint2;
				itemWeapon.Price = tempWeapon2.Price;
				itemWeapon.rank = tempWeapon2.rank;
				itemWeapon.hiddenID = tempWeapon2.hiddenID;
				itemWeapon.sockets = tempWeapon2.sockets;
				itemWeapon.ModelIndex = tempWeapon2.ModelIndex;
				itemWeapon.IconIndex = tempWeapon2.IconIndex;
				itemWeapon.Grade = tempWeapon2.Grade;
				itemWeapon.BlockDrop = tempWeapon2.BlockDrop;
				itemWeapon.BlockTrade = tempWeapon2.BlockTrade;
				itemWeapon.BlockNpcSell = tempWeapon2.BlockNpcSell;
				itemWeapon.CashCheck = tempWeapon2.CashCheck;
				itemWeapon.Time = tempWeapon2.Time;
				itemWeapon.BlockStorage = tempWeapon2.BlockStorage;
				itemWeapon.maxSlots = tempWeapon2.maxSlots;
				itemWeapon.reduceLevel = tempWeapon2.reduceLevel;
				for (int i = 0; i < 5; i++)
				{
					itemWeapon.effects[i].ID = tempWeapon2.effects[i].ID;
					itemWeapon.effects[i].Type = tempWeapon2.effects[i].Type;
					itemWeapon.effects[i].Value = tempWeapon2.effects[i].Value;
					itemWeapon.effects[i].Per = tempWeapon2.effects[i].Per;
					itemWeapon.effects[i].Prob = tempWeapon2.effects[i].Prob;
				}
				for (int j = 0; j < 4; j++)
				{
					itemWeapon.specials[j].ID = tempWeapon2.specials[j].ID;
					itemWeapon.specials[j].Type = tempWeapon2.specials[j].Type;
					itemWeapon.specials[j].Value = tempWeapon2.specials[j].Value;
					itemWeapon.specials[j].Time = tempWeapon2.specials[j].Time;
					itemWeapon.specials[j].Prob = tempWeapon2.specials[j].Prob;
				}
				for (int k = 0; k < 4; k++)
				{
					itemWeapon.vecItemPosition[k].x = tempWeapon2.vecItemPosition[k].x;
					itemWeapon.vecItemPosition[k].y = tempWeapon2.vecItemPosition[k].y;
					itemWeapon.vecItemPosition[k].z = tempWeapon2.vecItemPosition[k].z;
				}
				for (int l = 0; l < tempWeapon2.unknownBytes.Count; l++)
				{
					itemWeapon.unknownBytes[l] = tempWeapon2.unknownBytes[l];
				}
			}
			listBoxWeap_SelectedIndexChanged_1(this, EventArgs.Empty);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.comboBoxWep = new System.Windows.Forms.ComboBox();
		this.listBoxWeap = new System.Windows.Forms.ListBox();
		this.tabPage2 = new System.Windows.Forms.TabPage();
		this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
		this.checkBoxCantNPC = new System.Windows.Forms.CheckBox();
		this.checkBoxCantTrade = new System.Windows.Forms.CheckBox();
		this.checkBoxCantStorage = new System.Windows.Forms.CheckBox();
		this.checkBoxStatsTrade = new System.Windows.Forms.CheckBox();
		this.tabPage1 = new System.Windows.Forms.TabPage();
		this.dataGridView1 = new System.Windows.Forms.DataGridView();
		this.tabPageEffects = new System.Windows.Forms.TabPage();
		this.listBoxEffects = new System.Windows.Forms.ListBox();
		this.tabPageUnknownBytes = new System.Windows.Forms.TabPage();
		this.byteViewer = new System.ComponentModel.Design.ByteViewer();
		this.tabRequirements = new System.Windows.Forms.TabPage();
		this.tableLayoutPanel3 = new System.Windows.Forms.TableLayoutPanel();
		this.labelMainStat = new System.Windows.Forms.Label();
		this.labelSecondaryStat = new System.Windows.Forms.Label();
		this.textBoxSecondaryStat = new System.Windows.Forms.TextBox();
		this.textBoxMainStat = new System.Windows.Forms.TextBox();
		this.labelLevel = new System.Windows.Forms.Label();
		this.textBoxLevel = new System.Windows.Forms.TextBox();
		this.labelTextLevel = new System.Windows.Forms.Label();
		this.textBox1 = new System.Windows.Forms.TextBox();
		this.tabDetails = new System.Windows.Forms.TabPage();
		this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
		this.label5 = new System.Windows.Forms.Label();
		this.labelMinDmg = new System.Windows.Forms.Label();
		this.labelMaxDmg = new System.Windows.Forms.Label();
		this.labelDurability = new System.Windows.Forms.Label();
		this.labelBalance = new System.Windows.Forms.Label();
		this.labelAR = new System.Windows.Forms.Label();
		this.labelCrit = new System.Windows.Forms.Label();
		this.textBoxMinDmg = new System.Windows.Forms.TextBox();
		this.textBoxMaxDmg = new System.Windows.Forms.TextBox();
		this.textBoxDurability = new System.Windows.Forms.TextBox();
		this.textBoxBalance = new System.Windows.Forms.TextBox();
		this.textBoxAR = new System.Windows.Forms.TextBox();
		this.textBoxCrit = new System.Windows.Forms.TextBox();
		this.textBoxHardness = new System.Windows.Forms.TextBox();
		this.labelHardness = new System.Windows.Forms.Label();
		this.labelTears = new System.Windows.Forms.Label();
		this.textBoxTears = new System.Windows.Forms.TextBox();
		this.labelPrice = new System.Windows.Forms.Label();
		this.textBoxPrice = new System.Windows.Forms.TextBox();
		this.labelSockets = new System.Windows.Forms.Label();
		this.textBoxSockets = new System.Windows.Forms.TextBox();
		this.labelRank = new System.Windows.Forms.Label();
		this.textBoxRank = new System.Windows.Forms.TextBox();
		this.labelModel = new System.Windows.Forms.Label();
		this.labelIcon = new System.Windows.Forms.Label();
		this.textBoxModel = new System.Windows.Forms.TextBox();
		this.textBoxIcon = new System.Windows.Forms.TextBox();
		this.labelTime = new System.Windows.Forms.Label();
		this.textBoxTime = new System.Windows.Forms.TextBox();
		this.labelContribClan = new System.Windows.Forms.Label();
		this.labelContribOthers = new System.Windows.Forms.Label();
		this.textBoxContribClan = new System.Windows.Forms.TextBox();
		this.textBoxContribOthers = new System.Windows.Forms.TextBox();
		this.labelLvlRed = new System.Windows.Forms.Label();
		this.textBoxLvlRed = new System.Windows.Forms.TextBox();
		this.label2 = new System.Windows.Forms.Label();
		this.labelCashCheck = new System.Windows.Forms.Label();
		this.labelMaxSockets = new System.Windows.Forms.Label();
		this.textBoxMaxSlots = new System.Windows.Forms.TextBox();
		this.textBoxCashCheck = new System.Windows.Forms.TextBox();
		this.unknownByteIndex = new System.Windows.Forms.ComboBox();
		this.unknownByteValue = new System.Windows.Forms.TextBox();
		this.tabBasicInfo = new System.Windows.Forms.TabPage();
		this.tableLayoutPanel4 = new System.Windows.Forms.TableLayoutPanel();
		this.labelName = new System.Windows.Forms.Label();
		this.labelWeaponType = new System.Windows.Forms.Label();
		this.textBoxName = new System.Windows.Forms.TextBox();
		this.comboBoxWeaponType = new System.Windows.Forms.ComboBox();
		this.comboBoxWeaponThirdType = new System.Windows.Forms.ComboBox();
		this.label1 = new System.Windows.Forms.Label();
		this.buttonLoadIcon = new System.Windows.Forms.Button();
		this.labelIco = new System.Windows.Forms.Label();
		this.labelXSDindex = new System.Windows.Forms.Label();
		this.textBoxXSDindex = new System.Windows.Forms.TextBox();
		this.label3 = new System.Windows.Forms.Label();
		this.label4 = new System.Windows.Forms.Label();
		this.comboBoxNickname = new System.Windows.Forms.ComboBox();
		this.comboBoxAddTo = new System.Windows.Forms.ComboBox();
		this.buttonCopy = new System.Windows.Forms.Button();
		this.button1 = new System.Windows.Forms.Button();
		this.multipleItems = new System.Windows.Forms.TextBox();
		this.tabControl1 = new System.Windows.Forms.TabControl();
		this.btn_pasteItem = new System.Windows.Forms.Button();
		this.btn_copyItem = new System.Windows.Forms.Button();
		this.iDDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.nameDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.descriptionDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.valueDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.probDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.effectBindingSource = new System.Windows.Forms.BindingSource(this.components);
		this.itemWeaponBindingSource = new System.Windows.Forms.BindingSource(this.components);
		this.tabPage2.SuspendLayout();
		this.tableLayoutPanel1.SuspendLayout();
		this.tabPage1.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.dataGridView1).BeginInit();
		this.tabPageEffects.SuspendLayout();
		this.tabPageUnknownBytes.SuspendLayout();
		this.tabRequirements.SuspendLayout();
		this.tableLayoutPanel3.SuspendLayout();
		this.tabDetails.SuspendLayout();
		this.tableLayoutPanel2.SuspendLayout();
		this.tabBasicInfo.SuspendLayout();
		this.tableLayoutPanel4.SuspendLayout();
		this.tabControl1.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.effectBindingSource).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.itemWeaponBindingSource).BeginInit();
		base.SuspendLayout();
		this.comboBoxWep.Dock = System.Windows.Forms.DockStyle.Top;
		this.comboBoxWep.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
		this.comboBoxWep.FormattingEnabled = true;
		this.comboBoxWep.Items.AddRange(new object[3] { "Weapons", "Weapons2\t", "Weapons3" });
		this.comboBoxWep.Location = new System.Drawing.Point(0, 0);
		this.comboBoxWep.Name = "comboBoxWep";
		this.comboBoxWep.Size = new System.Drawing.Size(928, 21);
		this.comboBoxWep.TabIndex = 2;
		this.comboBoxWep.SelectedIndexChanged += new System.EventHandler(comboBoxWep_SelectedIndexChanged);
		this.listBoxWeap.FormattingEnabled = true;
		this.listBoxWeap.Location = new System.Drawing.Point(9, 39);
		this.listBoxWeap.Margin = new System.Windows.Forms.Padding(2);
		this.listBoxWeap.Name = "listBoxWeap";
		this.listBoxWeap.Size = new System.Drawing.Size(233, 407);
		this.listBoxWeap.TabIndex = 3;
		this.listBoxWeap.SelectedIndexChanged += new System.EventHandler(listBoxWeap_SelectedIndexChanged_1);
		this.tabPage2.Controls.Add(this.tableLayoutPanel1);
		this.tabPage2.Location = new System.Drawing.Point(4, 22);
		this.tabPage2.Name = "tabPage2";
		this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage2.Size = new System.Drawing.Size(635, 375);
		this.tabPage2.TabIndex = 6;
		this.tabPage2.Text = "Additional Options";
		this.tabPage2.UseVisualStyleBackColor = true;
		this.tableLayoutPanel1.ColumnCount = 1;
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel1.Controls.Add(this.checkBoxCantNPC, 0, 0);
		this.tableLayoutPanel1.Controls.Add(this.checkBoxCantTrade, 0, 2);
		this.tableLayoutPanel1.Controls.Add(this.checkBoxCantStorage, 0, 1);
		this.tableLayoutPanel1.Controls.Add(this.checkBoxStatsTrade, 0, 3);
		this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tableLayoutPanel1.Location = new System.Drawing.Point(3, 3);
		this.tableLayoutPanel1.Name = "tableLayoutPanel1";
		this.tableLayoutPanel1.RowCount = 4;
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20f));
		this.tableLayoutPanel1.Size = new System.Drawing.Size(629, 369);
		this.tableLayoutPanel1.TabIndex = 0;
		this.checkBoxCantNPC.AutoSize = true;
		this.checkBoxCantNPC.Location = new System.Drawing.Point(3, 3);
		this.checkBoxCantNPC.Name = "checkBoxCantNPC";
		this.checkBoxCantNPC.Size = new System.Drawing.Size(134, 17);
		this.checkBoxCantNPC.TabIndex = 0;
		this.checkBoxCantNPC.Text = "Cannot be sold at NPC";
		this.checkBoxCantNPC.UseVisualStyleBackColor = true;
		this.checkBoxCantNPC.CheckedChanged += new System.EventHandler(checkBoxCantNPC_CheckedChanged);
		this.checkBoxCantTrade.AutoSize = true;
		this.checkBoxCantTrade.Location = new System.Drawing.Point(3, 49);
		this.checkBoxCantTrade.Name = "checkBoxCantTrade";
		this.checkBoxCantTrade.Size = new System.Drawing.Size(188, 17);
		this.checkBoxCantTrade.TabIndex = 1;
		this.checkBoxCantTrade.Text = "Cannot be traded between players";
		this.checkBoxCantTrade.UseVisualStyleBackColor = true;
		this.checkBoxCantTrade.CheckedChanged += new System.EventHandler(checkBoxCantTrade_CheckedChanged);
		this.checkBoxCantStorage.AutoSize = true;
		this.checkBoxCantStorage.Location = new System.Drawing.Point(3, 26);
		this.checkBoxCantStorage.Name = "checkBoxCantStorage";
		this.checkBoxCantStorage.Size = new System.Drawing.Size(139, 17);
		this.checkBoxCantStorage.TabIndex = 2;
		this.checkBoxCantStorage.Text = "Cannot move to storage";
		this.checkBoxCantStorage.UseVisualStyleBackColor = true;
		this.checkBoxCantStorage.CheckedChanged += new System.EventHandler(checkBoxCantStorage_CheckedChanged);
		this.checkBoxStatsTrade.AutoSize = true;
		this.checkBoxStatsTrade.Location = new System.Drawing.Point(3, 72);
		this.checkBoxStatsTrade.Name = "checkBoxStatsTrade";
		this.checkBoxStatsTrade.Size = new System.Drawing.Size(151, 17);
		this.checkBoxStatsTrade.TabIndex = 3;
		this.checkBoxStatsTrade.Text = "Stats can be traded (0/30)";
		this.checkBoxStatsTrade.UseVisualStyleBackColor = true;
		this.checkBoxStatsTrade.CheckedChanged += new System.EventHandler(checkBoxStatsTrade_CheckedChanged);
		this.tabPage1.Controls.Add(this.dataGridView1);
		this.tabPage1.Location = new System.Drawing.Point(4, 22);
		this.tabPage1.Name = "tabPage1";
		this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage1.Size = new System.Drawing.Size(635, 375);
		this.tabPage1.TabIndex = 5;
		this.tabPage1.Text = "Eff Edit";
		this.tabPage1.UseVisualStyleBackColor = true;
		this.dataGridView1.AllowUserToAddRows = false;
		this.dataGridView1.AllowUserToDeleteRows = false;
		this.dataGridView1.AutoGenerateColumns = false;
		this.dataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
		this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
		this.dataGridView1.Columns.AddRange(this.iDDataGridViewTextBoxColumn, this.nameDataGridViewTextBoxColumn, this.descriptionDataGridViewTextBoxColumn, this.valueDataGridViewTextBoxColumn, this.probDataGridViewTextBoxColumn);
		this.dataGridView1.DataSource = this.effectBindingSource;
		this.dataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.dataGridView1.Location = new System.Drawing.Point(3, 3);
		this.dataGridView1.Name = "dataGridView1";
		this.dataGridView1.Size = new System.Drawing.Size(629, 369);
		this.dataGridView1.TabIndex = 0;
		this.dataGridView1.CellValueChanged += new System.Windows.Forms.DataGridViewCellEventHandler(dataGridView1_CellValueChanged);
		this.tabPageEffects.Controls.Add(this.listBoxEffects);
		this.tabPageEffects.Location = new System.Drawing.Point(4, 22);
		this.tabPageEffects.Name = "tabPageEffects";
		this.tabPageEffects.Padding = new System.Windows.Forms.Padding(3);
		this.tabPageEffects.Size = new System.Drawing.Size(635, 375);
		this.tabPageEffects.TabIndex = 4;
		this.tabPageEffects.Text = "Eff Display";
		this.tabPageEffects.UseVisualStyleBackColor = true;
		this.listBoxEffects.Dock = System.Windows.Forms.DockStyle.Fill;
		this.listBoxEffects.FormattingEnabled = true;
		this.listBoxEffects.Location = new System.Drawing.Point(3, 3);
		this.listBoxEffects.Name = "listBoxEffects";
		this.listBoxEffects.Size = new System.Drawing.Size(629, 369);
		this.listBoxEffects.TabIndex = 0;
		this.tabPageUnknownBytes.Controls.Add(this.byteViewer);
		this.tabPageUnknownBytes.Location = new System.Drawing.Point(4, 22);
		this.tabPageUnknownBytes.Name = "tabPageUnknownBytes";
		this.tabPageUnknownBytes.Padding = new System.Windows.Forms.Padding(3);
		this.tabPageUnknownBytes.Size = new System.Drawing.Size(635, 375);
		this.tabPageUnknownBytes.TabIndex = 3;
		this.tabPageUnknownBytes.Text = "Unknown bytes";
		this.tabPageUnknownBytes.UseVisualStyleBackColor = true;
		this.byteViewer.CellBorderStyle = System.Windows.Forms.TableLayoutPanelCellBorderStyle.Inset;
		this.byteViewer.ColumnCount = 1;
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.Location = new System.Drawing.Point(0, 0);
		this.byteViewer.Name = "byteViewer";
		this.byteViewer.RowCount = 1;
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100f));
		this.byteViewer.Size = new System.Drawing.Size(634, 514);
		this.byteViewer.TabIndex = 0;
		this.tabRequirements.Controls.Add(this.tableLayoutPanel3);
		this.tabRequirements.Location = new System.Drawing.Point(4, 22);
		this.tabRequirements.Name = "tabRequirements";
		this.tabRequirements.Padding = new System.Windows.Forms.Padding(3);
		this.tabRequirements.Size = new System.Drawing.Size(635, 375);
		this.tabRequirements.TabIndex = 1;
		this.tabRequirements.Text = "Requirements";
		this.tabRequirements.UseVisualStyleBackColor = true;
		this.tableLayoutPanel3.ColumnCount = 3;
		this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel3.Controls.Add(this.labelMainStat, 0, 1);
		this.tableLayoutPanel3.Controls.Add(this.labelSecondaryStat, 0, 2);
		this.tableLayoutPanel3.Controls.Add(this.textBoxSecondaryStat, 1, 2);
		this.tableLayoutPanel3.Controls.Add(this.textBoxMainStat, 1, 1);
		this.tableLayoutPanel3.Controls.Add(this.labelLevel, 0, 0);
		this.tableLayoutPanel3.Controls.Add(this.textBoxLevel, 1, 0);
		this.tableLayoutPanel3.Controls.Add(this.labelTextLevel, 2, 0);
		this.tableLayoutPanel3.Controls.Add(this.textBox1, 2, 3);
		this.tableLayoutPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tableLayoutPanel3.Location = new System.Drawing.Point(3, 3);
		this.tableLayoutPanel3.Name = "tableLayoutPanel3";
		this.tableLayoutPanel3.RowCount = 4;
		this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel3.Size = new System.Drawing.Size(629, 369);
		this.tableLayoutPanel3.TabIndex = 9;
		this.labelMainStat.AutoSize = true;
		this.labelMainStat.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelMainStat.Location = new System.Drawing.Point(3, 26);
		this.labelMainStat.Name = "labelMainStat";
		this.labelMainStat.Size = new System.Drawing.Size(64, 26);
		this.labelMainStat.TabIndex = 1;
		this.labelMainStat.Text = "Main stat";
		this.labelMainStat.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelSecondaryStat.AutoSize = true;
		this.labelSecondaryStat.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelSecondaryStat.Location = new System.Drawing.Point(3, 52);
		this.labelSecondaryStat.Name = "labelSecondaryStat";
		this.labelSecondaryStat.Size = new System.Drawing.Size(64, 26);
		this.labelSecondaryStat.TabIndex = 3;
		this.labelSecondaryStat.Text = "Second stat";
		this.labelSecondaryStat.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxSecondaryStat.Location = new System.Drawing.Point(73, 55);
		this.textBoxSecondaryStat.Name = "textBoxSecondaryStat";
		this.textBoxSecondaryStat.Size = new System.Drawing.Size(77, 20);
		this.textBoxSecondaryStat.TabIndex = 8;
		this.textBoxSecondaryStat.TextChanged += new System.EventHandler(textBoxSecondaryStat_TextChanged);
		this.textBoxMainStat.Location = new System.Drawing.Point(73, 29);
		this.textBoxMainStat.Name = "textBoxMainStat";
		this.textBoxMainStat.Size = new System.Drawing.Size(77, 20);
		this.textBoxMainStat.TabIndex = 7;
		this.textBoxMainStat.TextChanged += new System.EventHandler(textBoxMainStat_TextChanged);
		this.labelLevel.AutoSize = true;
		this.labelLevel.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelLevel.Location = new System.Drawing.Point(3, 0);
		this.labelLevel.Name = "labelLevel";
		this.labelLevel.Size = new System.Drawing.Size(64, 26);
		this.labelLevel.TabIndex = 9;
		this.labelLevel.Text = "Level";
		this.labelLevel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxLevel.Dock = System.Windows.Forms.DockStyle.Fill;
		this.textBoxLevel.Location = new System.Drawing.Point(73, 3);
		this.textBoxLevel.Name = "textBoxLevel";
		this.textBoxLevel.Size = new System.Drawing.Size(77, 20);
		this.textBoxLevel.TabIndex = 10;
		this.textBoxLevel.TextChanged += new System.EventHandler(textBoxLevel_TextChanged);
		this.labelTextLevel.AutoSize = true;
		this.labelTextLevel.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelTextLevel.Location = new System.Drawing.Point(156, 0);
		this.labelTextLevel.Name = "labelTextLevel";
		this.labelTextLevel.Size = new System.Drawing.Size(470, 26);
		this.labelTextLevel.TabIndex = 11;
		this.labelTextLevel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelTextLevel.TextChanged += new System.EventHandler(textBoxLevel_TextChanged);
		this.textBox1.Location = new System.Drawing.Point(156, 81);
		this.textBox1.Multiline = true;
		this.textBox1.Name = "textBox1";
		this.textBox1.Size = new System.Drawing.Size(181, 96);
		this.textBox1.TabIndex = 12;
		this.tabDetails.Controls.Add(this.tableLayoutPanel2);
		this.tabDetails.Location = new System.Drawing.Point(4, 22);
		this.tabDetails.Name = "tabDetails";
		this.tabDetails.Padding = new System.Windows.Forms.Padding(3);
		this.tabDetails.Size = new System.Drawing.Size(635, 375);
		this.tabDetails.TabIndex = 0;
		this.tabDetails.Text = "Details";
		this.tabDetails.UseVisualStyleBackColor = true;
		this.tableLayoutPanel2.ColumnCount = 5;
		this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20f));
		this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20f));
		this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20f));
		this.tableLayoutPanel2.Controls.Add(this.label5, 0, 12);
		this.tableLayoutPanel2.Controls.Add(this.labelMinDmg, 0, 0);
		this.tableLayoutPanel2.Controls.Add(this.labelMaxDmg, 0, 1);
		this.tableLayoutPanel2.Controls.Add(this.labelDurability, 0, 2);
		this.tableLayoutPanel2.Controls.Add(this.labelBalance, 0, 3);
		this.tableLayoutPanel2.Controls.Add(this.labelAR, 0, 4);
		this.tableLayoutPanel2.Controls.Add(this.labelCrit, 0, 5);
		this.tableLayoutPanel2.Controls.Add(this.textBoxMinDmg, 1, 0);
		this.tableLayoutPanel2.Controls.Add(this.textBoxMaxDmg, 1, 1);
		this.tableLayoutPanel2.Controls.Add(this.textBoxDurability, 1, 2);
		this.tableLayoutPanel2.Controls.Add(this.textBoxBalance, 1, 3);
		this.tableLayoutPanel2.Controls.Add(this.textBoxAR, 1, 4);
		this.tableLayoutPanel2.Controls.Add(this.textBoxCrit, 1, 5);
		this.tableLayoutPanel2.Controls.Add(this.textBoxHardness, 1, 7);
		this.tableLayoutPanel2.Controls.Add(this.labelHardness, 0, 7);
		this.tableLayoutPanel2.Controls.Add(this.labelTears, 0, 8);
		this.tableLayoutPanel2.Controls.Add(this.textBoxTears, 1, 8);
		this.tableLayoutPanel2.Controls.Add(this.labelPrice, 0, 10);
		this.tableLayoutPanel2.Controls.Add(this.textBoxPrice, 1, 10);
		this.tableLayoutPanel2.Controls.Add(this.labelSockets, 0, 11);
		this.tableLayoutPanel2.Controls.Add(this.textBoxSockets, 1, 11);
		this.tableLayoutPanel2.Controls.Add(this.labelRank, 3, 0);
		this.tableLayoutPanel2.Controls.Add(this.textBoxRank, 4, 0);
		this.tableLayoutPanel2.Controls.Add(this.labelModel, 3, 1);
		this.tableLayoutPanel2.Controls.Add(this.labelIcon, 3, 2);
		this.tableLayoutPanel2.Controls.Add(this.textBoxModel, 4, 1);
		this.tableLayoutPanel2.Controls.Add(this.textBoxIcon, 4, 2);
		this.tableLayoutPanel2.Controls.Add(this.labelTime, 3, 3);
		this.tableLayoutPanel2.Controls.Add(this.textBoxTime, 4, 3);
		this.tableLayoutPanel2.Controls.Add(this.labelContribClan, 3, 4);
		this.tableLayoutPanel2.Controls.Add(this.labelContribOthers, 3, 5);
		this.tableLayoutPanel2.Controls.Add(this.textBoxContribClan, 4, 4);
		this.tableLayoutPanel2.Controls.Add(this.textBoxContribOthers, 4, 5);
		this.tableLayoutPanel2.Controls.Add(this.labelLvlRed, 3, 7);
		this.tableLayoutPanel2.Controls.Add(this.textBoxLvlRed, 4, 7);
		this.tableLayoutPanel2.Controls.Add(this.label2, 3, 8);
		this.tableLayoutPanel2.Controls.Add(this.labelCashCheck, 3, 10);
		this.tableLayoutPanel2.Controls.Add(this.labelMaxSockets, 3, 11);
		this.tableLayoutPanel2.Controls.Add(this.textBoxMaxSlots, 4, 11);
		this.tableLayoutPanel2.Controls.Add(this.textBoxCashCheck, 4, 10);
		this.tableLayoutPanel2.Controls.Add(this.unknownByteIndex, 1, 12);
		this.tableLayoutPanel2.Controls.Add(this.unknownByteValue, 3, 12);
		this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tableLayoutPanel2.Location = new System.Drawing.Point(3, 3);
		this.tableLayoutPanel2.Name = "tableLayoutPanel2";
		this.tableLayoutPanel2.RowCount = 14;
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20f));
		this.tableLayoutPanel2.Size = new System.Drawing.Size(629, 369);
		this.tableLayoutPanel2.TabIndex = 0;
		this.label5.AutoSize = true;
		this.label5.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label5.Location = new System.Drawing.Point(3, 260);
		this.label5.Name = "label5";
		this.label5.Size = new System.Drawing.Size(79, 27);
		this.label5.TabIndex = 46;
		this.label5.Text = "UnknownBytes";
		this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelMinDmg.AutoSize = true;
		this.labelMinDmg.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelMinDmg.Location = new System.Drawing.Point(3, 0);
		this.labelMinDmg.Name = "labelMinDmg";
		this.labelMinDmg.Size = new System.Drawing.Size(79, 26);
		this.labelMinDmg.TabIndex = 7;
		this.labelMinDmg.Text = "Min dmg";
		this.labelMinDmg.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelMaxDmg.AutoSize = true;
		this.labelMaxDmg.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelMaxDmg.Location = new System.Drawing.Point(3, 26);
		this.labelMaxDmg.Name = "labelMaxDmg";
		this.labelMaxDmg.Size = new System.Drawing.Size(79, 26);
		this.labelMaxDmg.TabIndex = 8;
		this.labelMaxDmg.Text = "Max dmg";
		this.labelMaxDmg.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelDurability.AutoSize = true;
		this.labelDurability.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelDurability.Location = new System.Drawing.Point(3, 52);
		this.labelDurability.Name = "labelDurability";
		this.labelDurability.Size = new System.Drawing.Size(79, 26);
		this.labelDurability.TabIndex = 9;
		this.labelDurability.Text = "Durability";
		this.labelDurability.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelBalance.AutoSize = true;
		this.labelBalance.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelBalance.Location = new System.Drawing.Point(3, 78);
		this.labelBalance.Name = "labelBalance";
		this.labelBalance.Size = new System.Drawing.Size(79, 26);
		this.labelBalance.TabIndex = 10;
		this.labelBalance.Text = "Balance";
		this.labelBalance.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelAR.AutoSize = true;
		this.labelAR.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelAR.Location = new System.Drawing.Point(3, 104);
		this.labelAR.Name = "labelAR";
		this.labelAR.Size = new System.Drawing.Size(79, 26);
		this.labelAR.TabIndex = 11;
		this.labelAR.Text = "Attack Rate";
		this.labelAR.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelCrit.AutoSize = true;
		this.labelCrit.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelCrit.Location = new System.Drawing.Point(3, 130);
		this.labelCrit.Name = "labelCrit";
		this.labelCrit.Size = new System.Drawing.Size(79, 26);
		this.labelCrit.TabIndex = 12;
		this.labelCrit.Text = "Critical Rate";
		this.labelCrit.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxMinDmg.Location = new System.Drawing.Point(88, 3);
		this.textBoxMinDmg.Name = "textBoxMinDmg";
		this.textBoxMinDmg.Size = new System.Drawing.Size(100, 20);
		this.textBoxMinDmg.TabIndex = 13;
		this.textBoxMinDmg.Leave += new System.EventHandler(textBoxMinDmg_Leave);
		this.textBoxMaxDmg.Location = new System.Drawing.Point(88, 29);
		this.textBoxMaxDmg.Name = "textBoxMaxDmg";
		this.textBoxMaxDmg.Size = new System.Drawing.Size(100, 20);
		this.textBoxMaxDmg.TabIndex = 14;
		this.textBoxMaxDmg.TextChanged += new System.EventHandler(textBoxMaxDmg_TextChanged);
		this.textBoxDurability.Location = new System.Drawing.Point(88, 55);
		this.textBoxDurability.Name = "textBoxDurability";
		this.textBoxDurability.Size = new System.Drawing.Size(100, 20);
		this.textBoxDurability.TabIndex = 15;
		this.textBoxDurability.TextChanged += new System.EventHandler(textBoxDurability_TextChanged);
		this.textBoxBalance.Location = new System.Drawing.Point(88, 81);
		this.textBoxBalance.Name = "textBoxBalance";
		this.textBoxBalance.Size = new System.Drawing.Size(100, 20);
		this.textBoxBalance.TabIndex = 16;
		this.textBoxBalance.TextChanged += new System.EventHandler(textBoxBalance_TextChanged);
		this.textBoxAR.Location = new System.Drawing.Point(88, 107);
		this.textBoxAR.Name = "textBoxAR";
		this.textBoxAR.Size = new System.Drawing.Size(100, 20);
		this.textBoxAR.TabIndex = 17;
		this.textBoxAR.TextChanged += new System.EventHandler(textBoxAR_TextChanged);
		this.textBoxCrit.Location = new System.Drawing.Point(88, 133);
		this.textBoxCrit.Name = "textBoxCrit";
		this.textBoxCrit.Size = new System.Drawing.Size(100, 20);
		this.textBoxCrit.TabIndex = 18;
		this.textBoxCrit.TextChanged += new System.EventHandler(textBoxCrit_TextChanged);
		this.textBoxHardness.Location = new System.Drawing.Point(88, 159);
		this.textBoxHardness.Name = "textBoxHardness";
		this.textBoxHardness.Size = new System.Drawing.Size(100, 20);
		this.textBoxHardness.TabIndex = 19;
		this.textBoxHardness.TextChanged += new System.EventHandler(textBoxHardness_TextChanged);
		this.labelHardness.AutoSize = true;
		this.labelHardness.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelHardness.Location = new System.Drawing.Point(3, 156);
		this.labelHardness.Name = "labelHardness";
		this.labelHardness.Size = new System.Drawing.Size(79, 26);
		this.labelHardness.TabIndex = 20;
		this.labelHardness.Text = "Hardness";
		this.labelHardness.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelTears.AutoSize = true;
		this.labelTears.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelTears.Location = new System.Drawing.Point(3, 182);
		this.labelTears.Name = "labelTears";
		this.labelTears.Size = new System.Drawing.Size(79, 26);
		this.labelTears.TabIndex = 21;
		this.labelTears.Text = "Tears";
		this.labelTears.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxTears.Location = new System.Drawing.Point(88, 185);
		this.textBoxTears.Name = "textBoxTears";
		this.textBoxTears.Size = new System.Drawing.Size(100, 20);
		this.textBoxTears.TabIndex = 22;
		this.labelPrice.AutoSize = true;
		this.labelPrice.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelPrice.Location = new System.Drawing.Point(3, 208);
		this.labelPrice.Name = "labelPrice";
		this.labelPrice.Size = new System.Drawing.Size(79, 26);
		this.labelPrice.TabIndex = 23;
		this.labelPrice.Text = "Price";
		this.labelPrice.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxPrice.Location = new System.Drawing.Point(88, 211);
		this.textBoxPrice.Name = "textBoxPrice";
		this.textBoxPrice.Size = new System.Drawing.Size(100, 20);
		this.textBoxPrice.TabIndex = 24;
		this.textBoxPrice.TextChanged += new System.EventHandler(textBoxPrice_TextChanged);
		this.labelSockets.AutoSize = true;
		this.labelSockets.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelSockets.Location = new System.Drawing.Point(3, 234);
		this.labelSockets.Name = "labelSockets";
		this.labelSockets.Size = new System.Drawing.Size(79, 26);
		this.labelSockets.TabIndex = 25;
		this.labelSockets.Text = "Sockets";
		this.labelSockets.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxSockets.Location = new System.Drawing.Point(88, 237);
		this.textBoxSockets.Name = "textBoxSockets";
		this.textBoxSockets.Size = new System.Drawing.Size(100, 20);
		this.textBoxSockets.TabIndex = 26;
		this.textBoxSockets.TextChanged += new System.EventHandler(textBoxSockets_TextChanged);
		this.labelRank.AutoSize = true;
		this.labelRank.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelRank.Location = new System.Drawing.Point(235, 0);
		this.labelRank.Name = "labelRank";
		this.labelRank.Size = new System.Drawing.Size(110, 26);
		this.labelRank.TabIndex = 27;
		this.labelRank.Text = "Rank";
		this.labelRank.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxRank.Location = new System.Drawing.Point(351, 3);
		this.textBoxRank.Name = "textBoxRank";
		this.textBoxRank.Size = new System.Drawing.Size(100, 20);
		this.textBoxRank.TabIndex = 28;
		this.textBoxRank.TextChanged += new System.EventHandler(textBoxRank_TextChanged);
		this.labelModel.AutoSize = true;
		this.labelModel.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelModel.Location = new System.Drawing.Point(235, 26);
		this.labelModel.Name = "labelModel";
		this.labelModel.Size = new System.Drawing.Size(110, 26);
		this.labelModel.TabIndex = 29;
		this.labelModel.Text = "Model index";
		this.labelModel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelIcon.AutoSize = true;
		this.labelIcon.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelIcon.Location = new System.Drawing.Point(235, 52);
		this.labelIcon.Name = "labelIcon";
		this.labelIcon.Size = new System.Drawing.Size(110, 26);
		this.labelIcon.TabIndex = 30;
		this.labelIcon.Text = "Icon index";
		this.labelIcon.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxModel.Location = new System.Drawing.Point(351, 29);
		this.textBoxModel.Name = "textBoxModel";
		this.textBoxModel.Size = new System.Drawing.Size(100, 20);
		this.textBoxModel.TabIndex = 31;
		this.textBoxModel.TextChanged += new System.EventHandler(textBoxModel_TextChanged);
		this.textBoxIcon.Location = new System.Drawing.Point(351, 55);
		this.textBoxIcon.Name = "textBoxIcon";
		this.textBoxIcon.Size = new System.Drawing.Size(100, 20);
		this.textBoxIcon.TabIndex = 32;
		this.textBoxIcon.TextChanged += new System.EventHandler(textBoxIcon_TextChanged);
		this.labelTime.AutoSize = true;
		this.labelTime.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelTime.Location = new System.Drawing.Point(235, 78);
		this.labelTime.Name = "labelTime";
		this.labelTime.Size = new System.Drawing.Size(110, 26);
		this.labelTime.TabIndex = 33;
		this.labelTime.Text = "Time";
		this.labelTime.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxTime.Location = new System.Drawing.Point(351, 81);
		this.textBoxTime.Name = "textBoxTime";
		this.textBoxTime.Size = new System.Drawing.Size(100, 20);
		this.textBoxTime.TabIndex = 34;
		this.textBoxTime.TextChanged += new System.EventHandler(textBoxTime_TextChanged);
		this.labelContribClan.AutoSize = true;
		this.labelContribClan.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelContribClan.Location = new System.Drawing.Point(235, 104);
		this.labelContribClan.Name = "labelContribClan";
		this.labelContribClan.Size = new System.Drawing.Size(110, 26);
		this.labelContribClan.TabIndex = 35;
		this.labelContribClan.Text = "Clan contribution";
		this.labelContribClan.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelContribOthers.AutoSize = true;
		this.labelContribOthers.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelContribOthers.Location = new System.Drawing.Point(235, 130);
		this.labelContribOthers.Name = "labelContribOthers";
		this.labelContribOthers.Size = new System.Drawing.Size(110, 26);
		this.labelContribOthers.TabIndex = 36;
		this.labelContribOthers.Text = "Contribution for others";
		this.labelContribOthers.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxContribClan.Location = new System.Drawing.Point(351, 107);
		this.textBoxContribClan.Name = "textBoxContribClan";
		this.textBoxContribClan.Size = new System.Drawing.Size(100, 20);
		this.textBoxContribClan.TabIndex = 37;
		this.textBoxContribClan.TextChanged += new System.EventHandler(textBoxContribClan_TextChanged);
		this.textBoxContribOthers.Location = new System.Drawing.Point(351, 133);
		this.textBoxContribOthers.Name = "textBoxContribOthers";
		this.textBoxContribOthers.Size = new System.Drawing.Size(100, 20);
		this.textBoxContribOthers.TabIndex = 38;
		this.textBoxContribOthers.TextChanged += new System.EventHandler(textBoxContribOthers_TextChanged);
		this.labelLvlRed.AutoSize = true;
		this.labelLvlRed.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelLvlRed.Location = new System.Drawing.Point(235, 156);
		this.labelLvlRed.Name = "labelLvlRed";
		this.labelLvlRed.Size = new System.Drawing.Size(110, 26);
		this.labelLvlRed.TabIndex = 39;
		this.labelLvlRed.Text = "Level reducement";
		this.labelLvlRed.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxLvlRed.Location = new System.Drawing.Point(351, 159);
		this.textBoxLvlRed.Name = "textBoxLvlRed";
		this.textBoxLvlRed.Size = new System.Drawing.Size(100, 20);
		this.textBoxLvlRed.TabIndex = 40;
		this.textBoxLvlRed.TextChanged += new System.EventHandler(textBoxLvlRed_TextChanged);
		this.label2.AutoSize = true;
		this.label2.Location = new System.Drawing.Point(235, 182);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(35, 13);
		this.label2.TabIndex = 41;
		this.label2.Text = "label2";
		this.labelCashCheck.AutoSize = true;
		this.labelCashCheck.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelCashCheck.Location = new System.Drawing.Point(235, 208);
		this.labelCashCheck.Name = "labelCashCheck";
		this.labelCashCheck.Size = new System.Drawing.Size(110, 26);
		this.labelCashCheck.TabIndex = 42;
		this.labelCashCheck.Text = "Cash check (?)";
		this.labelCashCheck.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelMaxSockets.AutoSize = true;
		this.labelMaxSockets.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelMaxSockets.Location = new System.Drawing.Point(235, 234);
		this.labelMaxSockets.Name = "labelMaxSockets";
		this.labelMaxSockets.Size = new System.Drawing.Size(110, 26);
		this.labelMaxSockets.TabIndex = 43;
		this.labelMaxSockets.Text = "Max sockets";
		this.labelMaxSockets.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxMaxSlots.Location = new System.Drawing.Point(351, 237);
		this.textBoxMaxSlots.Name = "textBoxMaxSlots";
		this.textBoxMaxSlots.Size = new System.Drawing.Size(100, 20);
		this.textBoxMaxSlots.TabIndex = 44;
		this.textBoxMaxSlots.TextChanged += new System.EventHandler(textBoxMaxSlots_TextChanged);
		this.textBoxCashCheck.Location = new System.Drawing.Point(351, 211);
		this.textBoxCashCheck.Name = "textBoxCashCheck";
		this.textBoxCashCheck.Size = new System.Drawing.Size(100, 20);
		this.textBoxCashCheck.TabIndex = 45;
		this.unknownByteIndex.FormattingEnabled = true;
		this.unknownByteIndex.Location = new System.Drawing.Point(88, 263);
		this.unknownByteIndex.Name = "unknownByteIndex";
		this.unknownByteIndex.Size = new System.Drawing.Size(121, 21);
		this.unknownByteIndex.TabIndex = 47;
		this.unknownByteIndex.SelectedIndexChanged += new System.EventHandler(unknownByteIndex_SelectedIndexChanged);
		this.unknownByteValue.Location = new System.Drawing.Point(235, 263);
		this.unknownByteValue.Name = "unknownByteValue";
		this.unknownByteValue.Size = new System.Drawing.Size(100, 20);
		this.unknownByteValue.TabIndex = 48;
		this.unknownByteValue.TextChanged += new System.EventHandler(unknownByteValue_TextChanged);
		this.tabBasicInfo.Controls.Add(this.tableLayoutPanel4);
		this.tabBasicInfo.Location = new System.Drawing.Point(4, 22);
		this.tabBasicInfo.Name = "tabBasicInfo";
		this.tabBasicInfo.Padding = new System.Windows.Forms.Padding(3);
		this.tabBasicInfo.Size = new System.Drawing.Size(635, 375);
		this.tabBasicInfo.TabIndex = 2;
		this.tabBasicInfo.Text = "Basic";
		this.tabBasicInfo.UseVisualStyleBackColor = true;
		this.tabBasicInfo.Click += new System.EventHandler(tabBasicInfo_Click);
		this.tableLayoutPanel4.ColumnCount = 4;
		this.tableLayoutPanel4.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel4.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel4.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel4.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel4.Controls.Add(this.labelName, 0, 0);
		this.tableLayoutPanel4.Controls.Add(this.labelWeaponType, 0, 3);
		this.tableLayoutPanel4.Controls.Add(this.textBoxName, 1, 0);
		this.tableLayoutPanel4.Controls.Add(this.comboBoxWeaponType, 1, 3);
		this.tableLayoutPanel4.Controls.Add(this.comboBoxWeaponThirdType, 1, 4);
		this.tableLayoutPanel4.Controls.Add(this.label1, 0, 4);
		this.tableLayoutPanel4.Controls.Add(this.buttonLoadIcon, 1, 8);
		this.tableLayoutPanel4.Controls.Add(this.labelIco, 2, 8);
		this.tableLayoutPanel4.Controls.Add(this.labelXSDindex, 2, 0);
		this.tableLayoutPanel4.Controls.Add(this.textBoxXSDindex, 3, 0);
		this.tableLayoutPanel4.Controls.Add(this.label3, 0, 1);
		this.tableLayoutPanel4.Controls.Add(this.label4, 0, 2);
		this.tableLayoutPanel4.Controls.Add(this.comboBoxNickname, 1, 1);
		this.tableLayoutPanel4.Controls.Add(this.comboBoxAddTo, 1, 2);
		this.tableLayoutPanel4.Controls.Add(this.buttonCopy, 3, 8);
		this.tableLayoutPanel4.Controls.Add(this.button1, 1, 9);
		this.tableLayoutPanel4.Controls.Add(this.multipleItems, 3, 9);
		this.tableLayoutPanel4.Controls.Add(this.btn_copyItem, 1, 10);
		this.tableLayoutPanel4.Controls.Add(this.btn_pasteItem, 1, 11);
		this.tableLayoutPanel4.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tableLayoutPanel4.Location = new System.Drawing.Point(3, 3);
		this.tableLayoutPanel4.Name = "tableLayoutPanel4";
		this.tableLayoutPanel4.RowCount = 12;
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 48f));
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28f));
		this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 14f));
		this.tableLayoutPanel4.Size = new System.Drawing.Size(629, 369);
		this.tableLayoutPanel4.TabIndex = 0;
		this.labelName.AutoSize = true;
		this.labelName.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelName.Location = new System.Drawing.Point(3, 0);
		this.labelName.Name = "labelName";
		this.labelName.Size = new System.Drawing.Size(101, 26);
		this.labelName.TabIndex = 0;
		this.labelName.Text = "Name";
		this.labelName.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelWeaponType.AutoSize = true;
		this.labelWeaponType.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelWeaponType.Location = new System.Drawing.Point(3, 80);
		this.labelWeaponType.Name = "labelWeaponType";
		this.labelWeaponType.Size = new System.Drawing.Size(101, 27);
		this.labelWeaponType.TabIndex = 1;
		this.labelWeaponType.Text = "Weapon Main Type";
		this.labelWeaponType.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxName.Dock = System.Windows.Forms.DockStyle.Fill;
		this.textBoxName.Location = new System.Drawing.Point(110, 3);
		this.textBoxName.Name = "textBoxName";
		this.textBoxName.ReadOnly = true;
		this.textBoxName.Size = new System.Drawing.Size(123, 20);
		this.textBoxName.TabIndex = 2;
		this.comboBoxWeaponType.Dock = System.Windows.Forms.DockStyle.Fill;
		this.comboBoxWeaponType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
		this.comboBoxWeaponType.FormattingEnabled = true;
		this.comboBoxWeaponType.Items.AddRange(new object[6] { "SHORT", "LONG", "SOFT", "HIDDEN", "MUSICAL", "SPECIAL" });
		this.comboBoxWeaponType.Location = new System.Drawing.Point(110, 83);
		this.comboBoxWeaponType.Name = "comboBoxWeaponType";
		this.comboBoxWeaponType.Size = new System.Drawing.Size(123, 21);
		this.comboBoxWeaponType.TabIndex = 3;
		this.comboBoxWeaponThirdType.Dock = System.Windows.Forms.DockStyle.Fill;
		this.comboBoxWeaponThirdType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
		this.comboBoxWeaponThirdType.FormattingEnabled = true;
		this.comboBoxWeaponThirdType.Location = new System.Drawing.Point(110, 110);
		this.comboBoxWeaponThirdType.Name = "comboBoxWeaponThirdType";
		this.comboBoxWeaponThirdType.Size = new System.Drawing.Size(123, 21);
		this.comboBoxWeaponThirdType.TabIndex = 4;
		this.label1.AutoSize = true;
		this.label1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label1.Location = new System.Drawing.Point(3, 107);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(101, 27);
		this.label1.TabIndex = 5;
		this.label1.Text = "Weapon Type";
		this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.buttonLoadIcon.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.buttonLoadIcon.Location = new System.Drawing.Point(110, 137);
		this.buttonLoadIcon.Name = "buttonLoadIcon";
		this.buttonLoadIcon.Size = new System.Drawing.Size(64, 32);
		this.buttonLoadIcon.TabIndex = 6;
		this.buttonLoadIcon.Text = "Load icon";
		this.buttonLoadIcon.UseVisualStyleBackColor = true;
		this.labelIco.AutoSize = true;
		this.labelIco.Location = new System.Drawing.Point(239, 134);
		this.labelIco.Name = "labelIco";
		this.labelIco.Size = new System.Drawing.Size(0, 13);
		this.labelIco.TabIndex = 7;
		this.labelXSDindex.AutoSize = true;
		this.labelXSDindex.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelXSDindex.Location = new System.Drawing.Point(239, 0);
		this.labelXSDindex.Name = "labelXSDindex";
		this.labelXSDindex.Size = new System.Drawing.Size(57, 26);
		this.labelXSDindex.TabIndex = 8;
		this.labelXSDindex.Text = "XSD index";
		this.labelXSDindex.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxXSDindex.Location = new System.Drawing.Point(302, 3);
		this.textBoxXSDindex.Name = "textBoxXSDindex";
		this.textBoxXSDindex.Size = new System.Drawing.Size(100, 20);
		this.textBoxXSDindex.TabIndex = 9;
		this.label3.AutoSize = true;
		this.label3.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label3.Location = new System.Drawing.Point(3, 26);
		this.label3.Name = "label3";
		this.label3.Size = new System.Drawing.Size(101, 27);
		this.label3.TabIndex = 10;
		this.label3.Text = "Nickname";
		this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label4.AutoSize = true;
		this.label4.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label4.Location = new System.Drawing.Point(3, 53);
		this.label4.Name = "label4";
		this.label4.Size = new System.Drawing.Size(101, 27);
		this.label4.TabIndex = 11;
		this.label4.Text = "Add to";
		this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.comboBoxNickname.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
		this.comboBoxNickname.FormattingEnabled = true;
		this.comboBoxNickname.Location = new System.Drawing.Point(110, 29);
		this.comboBoxNickname.Name = "comboBoxNickname";
		this.comboBoxNickname.Size = new System.Drawing.Size(121, 21);
		this.comboBoxNickname.TabIndex = 12;
		this.comboBoxNickname.SelectedIndexChanged += new System.EventHandler(comboBoxNickname_SelectedIndexChanged);
		this.comboBoxAddTo.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
		this.comboBoxAddTo.FormattingEnabled = true;
		this.comboBoxAddTo.Location = new System.Drawing.Point(110, 56);
		this.comboBoxAddTo.Name = "comboBoxAddTo";
		this.comboBoxAddTo.Size = new System.Drawing.Size(121, 21);
		this.comboBoxAddTo.TabIndex = 13;
		this.buttonCopy.Location = new System.Drawing.Point(302, 137);
		this.buttonCopy.Name = "buttonCopy";
		this.buttonCopy.Size = new System.Drawing.Size(122, 23);
		this.buttonCopy.TabIndex = 14;
		this.buttonCopy.Text = "Copy to new weapon";
		this.buttonCopy.UseVisualStyleBackColor = true;
		this.buttonCopy.Click += new System.EventHandler(buttonCopy_Click);
		this.button1.Location = new System.Drawing.Point(110, 175);
		this.button1.Name = "button1";
		this.button1.Size = new System.Drawing.Size(121, 36);
		this.button1.TabIndex = 15;
		this.button1.Text = "Paste Effects To Multiple Items";
		this.button1.UseVisualStyleBackColor = true;
		this.button1.Click += new System.EventHandler(button1_Click);
		this.multipleItems.Location = new System.Drawing.Point(302, 175);
		this.multipleItems.Name = "multipleItems";
		this.multipleItems.Size = new System.Drawing.Size(326, 20);
		this.multipleItems.TabIndex = 16;
		this.tabControl1.Anchor = System.Windows.Forms.AnchorStyles.None;
		this.tabControl1.Controls.Add(this.tabBasicInfo);
		this.tabControl1.Controls.Add(this.tabDetails);
		this.tabControl1.Controls.Add(this.tabRequirements);
		this.tabControl1.Controls.Add(this.tabPageEffects);
		this.tabControl1.Controls.Add(this.tabPage1);
		this.tabControl1.Controls.Add(this.tabPage2);
		this.tabControl1.Controls.Add(this.tabPageUnknownBytes);
		this.tabControl1.Location = new System.Drawing.Point(260, 39);
		this.tabControl1.Name = "tabControl1";
		this.tabControl1.SelectedIndex = 0;
		this.tabControl1.Size = new System.Drawing.Size(643, 401);
		this.tabControl1.TabIndex = 2;
		this.btn_pasteItem.Enabled = false;
		this.btn_pasteItem.Location = new System.Drawing.Point(110, 251);
		this.btn_pasteItem.Name = "btn_pasteItem";
		this.btn_pasteItem.Size = new System.Drawing.Size(123, 23);
		this.btn_pasteItem.TabIndex = 57;
		this.btn_pasteItem.Text = "Paste Item";
		this.btn_pasteItem.UseVisualStyleBackColor = false;
		this.btn_pasteItem.Click += new System.EventHandler(btn_pasteItem_Click);
		this.btn_copyItem.Location = new System.Drawing.Point(110, 223);
		this.btn_copyItem.Name = "btn_copyItem";
		this.btn_copyItem.Size = new System.Drawing.Size(123, 22);
		this.btn_copyItem.TabIndex = 56;
		this.btn_copyItem.Text = "Copy Item";
		this.btn_copyItem.UseVisualStyleBackColor = false;
		this.btn_copyItem.Click += new System.EventHandler(btn_copyItem_Click);
		this.iDDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
		this.iDDataGridViewTextBoxColumn.DataPropertyName = "ID";
		this.iDDataGridViewTextBoxColumn.HeaderText = "ID";
		this.iDDataGridViewTextBoxColumn.Name = "iDDataGridViewTextBoxColumn";
		this.iDDataGridViewTextBoxColumn.Width = 43;
		this.nameDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
		this.nameDataGridViewTextBoxColumn.DataPropertyName = "Name";
		this.nameDataGridViewTextBoxColumn.HeaderText = "Name";
		this.nameDataGridViewTextBoxColumn.Name = "nameDataGridViewTextBoxColumn";
		this.nameDataGridViewTextBoxColumn.Resizable = System.Windows.Forms.DataGridViewTriState.True;
		this.nameDataGridViewTextBoxColumn.Width = 60;
		this.descriptionDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
		this.descriptionDataGridViewTextBoxColumn.DataPropertyName = "Description";
		this.descriptionDataGridViewTextBoxColumn.HeaderText = "Description";
		this.descriptionDataGridViewTextBoxColumn.Name = "descriptionDataGridViewTextBoxColumn";
		this.descriptionDataGridViewTextBoxColumn.ReadOnly = true;
		this.descriptionDataGridViewTextBoxColumn.Width = 85;
		this.valueDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
		this.valueDataGridViewTextBoxColumn.DataPropertyName = "Value";
		this.valueDataGridViewTextBoxColumn.HeaderText = "Value";
		this.valueDataGridViewTextBoxColumn.Name = "valueDataGridViewTextBoxColumn";
		this.valueDataGridViewTextBoxColumn.Width = 59;
		this.probDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
		this.probDataGridViewTextBoxColumn.DataPropertyName = "Prob";
		this.probDataGridViewTextBoxColumn.HeaderText = "Probability";
		this.probDataGridViewTextBoxColumn.Name = "probDataGridViewTextBoxColumn";
		this.probDataGridViewTextBoxColumn.Width = 80;
		this.effectBindingSource.DataSource = typeof(ItemTableReader.Effect);
		this.itemWeaponBindingSource.DataSource = typeof(ItemTableReader.ItemWeapon);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(928, 481);
		base.Controls.Add(this.listBoxWeap);
		base.Controls.Add(this.tabControl1);
		base.Controls.Add(this.comboBoxWep);
		base.MaximizeBox = false;
		base.MinimizeBox = false;
		base.Name = "FormWeapons";
		base.ShowIcon = false;
		this.Text = "Weapon Editor";
		base.Load += new System.EventHandler(FormWeapons_Load);
		base.KeyDown += new System.Windows.Forms.KeyEventHandler(FormWeapons_KeyDown);
		this.tabPage2.ResumeLayout(false);
		this.tableLayoutPanel1.ResumeLayout(false);
		this.tableLayoutPanel1.PerformLayout();
		this.tabPage1.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.dataGridView1).EndInit();
		this.tabPageEffects.ResumeLayout(false);
		this.tabPageUnknownBytes.ResumeLayout(false);
		this.tabRequirements.ResumeLayout(false);
		this.tableLayoutPanel3.ResumeLayout(false);
		this.tableLayoutPanel3.PerformLayout();
		this.tabDetails.ResumeLayout(false);
		this.tableLayoutPanel2.ResumeLayout(false);
		this.tableLayoutPanel2.PerformLayout();
		this.tabBasicInfo.ResumeLayout(false);
		this.tableLayoutPanel4.ResumeLayout(false);
		this.tableLayoutPanel4.PerformLayout();
		this.tabControl1.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.effectBindingSource).EndInit();
		((System.ComponentModel.ISupportInitialize)this.itemWeaponBindingSource).EndInit();
		base.ResumeLayout(false);
	}

	private void ComboBoxNickname_SelectedIndexChanged(object sender, EventArgs e)
	{
		throw new NotImplementedException();
	}
}
