using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Windows.Forms;

namespace ItemTableReader;

public class FormAccessories : Form
{
	private static ItemAccessory tempAccessory = new ItemAccessory();

	private static ItemAccessory tempAccessory2 = new ItemAccessory();

	private IContainer components;

	private ListBox listBoxAccessories;

	private TabControl tabControl1;

	private TabPage tabPage1;

	private TabPage tabPageUnknown;

	private ByteViewer byteViewer = new ByteViewer();

	private Button btnIcon;

	private TabPage tabPageRequirements;

	private TableLayoutPanel tableLayoutPanel1;

	private Label label1;

	private Label label2;

	private Label labelTxtLv;

	private Label label5;

	private Label label6;

	private Label label3;

	private Label label4;

	private TextBox textBoxLevel;

	private TextBox textBoxStr;

	private TextBox textBoxEss;

	private TextBox textBoxWis;

	private TextBox textBoxCon;

	private TextBox textBoxDex;

	private TabPage tabPageDetails;

	private TableLayoutPanel tableLayoutPanel2;

	private Label label7;

	private Label label8;

	private Label label9;

	private TextBox textBoxPrice;

	private TextBox textBoxCashCheck;

	private TextBox textBoxTime;

	private Label label10;

	private TextBox textBoxItemSet;

	private Label label11;

	private TextBox textBoxMaxSockets;

	private TabPage tabPage2;

	private CheckBox checkBoxNPC;

	private CheckBox checkBoxTrade;

	private CheckBox checkBoxDrop;

	private CheckBox checkBoxStore;

	private TabPage tabPageEffects;

	private DataGridView dataGridView1;

	private BindingSource effectBindingSource;

	private BindingSource specialBindingSource;

	private DataGridViewTextBoxColumn iDDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn valueDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn descriptionDataGridViewTextBoxColumn;

	private DataGridViewTextBoxColumn nameDataGridViewTextBoxColumn;

	private TableLayoutPanel tableLayoutPanel3;

	private Label label12;

	private TextBox textBoxName;

	private Label labelType;

	private ComboBox comboBox2ndType;

	private Label labelModelIndex;

	private Label labelIconIndex;

	private Label labelItemRank;

	private Label labelGroup;

	private Label labelCase;

	private Label labelGrade;

	private TextBox textBoxModelIndex;

	private TextBox textBoxIconIndex;

	private TextBox textBoxItemRank;

	private TextBox textBoxGroup;

	private TextBox textBoxCase;

	private TextBox textBoxGrade;

	private Label labelFame;

	private ComboBox comboBoxFame;

	private TextBox tbSecondaryType;

	private TextBox tbKarma;

	private Label label13;

	private ComboBox unknownByteIndex;

	private TextBox unknownByteValue;

	private Label label14;

	private TextBox textBoxXSDIndex;

	private Button btnCopyItem;

	private Button btnPasteItem;

	private Button btnCopyEffects;

	private Button btnPasteEffects;

	public FormAccessories()
	{
		InitializeComponent();
		for (int i = 0; i < 17; i++)
		{
			unknownByteIndex.Items.Add(i.ToString());
		}
	}

	private void FormAccessories_Load(object sender, EventArgs e)
	{
		tabPageUnknown.Controls.Add(byteViewer);
		byteViewer.Dock = DockStyle.Fill;
		listBoxAccessories.BeginUpdate();
		foreach (KeyValuePair<short, ItemAccessory> accessory in ItemAccessory.Accessories)
		{
			listBoxAccessories.Items.Add(accessory.Value);
		}
		listBoxAccessories.EndUpdate();
		listBoxAccessories.DisplayMember = "FullName";
		listBoxAccessories.ValueMember = "ID";
		comboBox2ndType.DataSource = Enum.GetValues(typeof(AccessorySecondType));
		comboBoxFame.DataSource = Enum.GetValues(typeof(FameValues));
	}

	private void listBoxAccessories_SelectedIndexChanged(object sender, EventArgs e)
	{
		ItemAccessory itemAccessory = listBoxAccessories.SelectedItem as ItemAccessory;
		tbSecondaryType.Text = itemAccessory.SecondType.ToString();
		comboBox2ndType.SelectedItem = (AccessorySecondType)itemAccessory.SecondType;
		tbKarma.Text = itemAccessory.Fame.ToString();
		textBoxXSDIndex.Text = itemAccessory.xsdName.ToString();
		byteViewer.Dock = DockStyle.Fill;
		byteViewer.SetBytes(itemAccessory.unknownBytes.ToArray());
		btnIcon.Image = ItemParser.SetIcon(itemAccessory);
		textBoxName.Text = itemAccessory.Name;
		textBoxCase.Text = itemAccessory.Case.ToString();
		textBoxModelIndex.Text = itemAccessory.ModelIndex.ToString();
		textBoxIconIndex.Text = itemAccessory.IconIndex.ToString();
		textBoxItemRank.Text = itemAccessory.ItemRank.ToString();
		textBoxGroup.Text = itemAccessory.Group.ToString();
		textBoxGrade.Text = itemAccessory.Grade.ToString();
		textBoxLevel.Text = itemAccessory.RequiredLevel.ToString();
		textBoxCon.Text = itemAccessory.RequiredCon.ToString();
		textBoxEss.Text = itemAccessory.RequiredEss.ToString();
		textBoxDex.Text = itemAccessory.RequiredDex.ToString();
		textBoxStr.Text = itemAccessory.RequiredStr.ToString();
		textBoxWis.Text = itemAccessory.RequiredWis.ToString();
		textBoxTime.Text = itemAccessory.Time.ToString();
		textBoxCashCheck.Text = itemAccessory.CashCheck.ToString();
		textBoxPrice.Text = itemAccessory.Price.ToString();
		textBoxItemSet.Text = itemAccessory.ItemSet.ToString();
		textBoxMaxSockets.Text = itemAccessory.MaxSockets.ToString();
		checkBoxDrop.Checked = itemAccessory.BlockDrop;
		checkBoxNPC.Checked = itemAccessory.BlockNpcSell;
		checkBoxStore.Checked = itemAccessory.BlockStorage;
		checkBoxTrade.Checked = Convert.ToBoolean(itemAccessory.BlockTrade);
		dataGridView1.DataSource = itemAccessory.Effects;
		if (unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemAccessory.unknownBytes.Count)
		{
			unknownByteValue.Text = itemAccessory.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
		}
	}

	private void textBoxLevel_TextChanged(object sender, EventArgs e)
	{
		labelTxtLv.Text = Level.Get(Convert.ToInt16(textBoxLevel.Text));
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).requiredLevel = Convert.ToByte(textBoxLevel.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void dataGridView1_CellValueChanged(object sender, DataGridViewCellEventArgs e)
	{
		if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory)
		{
			try
			{
				int key = Convert.ToInt32(dataGridView1[0, e.RowIndex].Value.ToString());
				itemAccessory.Effects[e.RowIndex].Desc = XsdManager.EffectsInfo[key];
				itemAccessory.Effects[e.RowIndex].Name = XsdManager.EffectsName[key];
			}
			catch
			{
			}
			Update();
		}
	}

	private void FormAccessories_KeyDown(object sender, KeyEventArgs e)
	{
		if (e.KeyCode == Keys.Up)
		{
			if (listBoxAccessories.SelectedIndex > 0)
			{
				listBoxAccessories.SelectedIndex--;
			}
		}
		else if (e.KeyCode == Keys.Down && listBoxAccessories.SelectedIndex < listBoxAccessories.Items.Count - 1)
		{
			listBoxAccessories.SelectedIndex++;
		}
	}

	private void textBoxModelIndex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).ModelIndex = Convert.ToInt16(textBoxModelIndex.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void tbSecondaryType_TextChanged(object sender, EventArgs e)
	{
	}

	private void textBoxIconIndex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).IconIndex = Convert.ToInt16(textBoxIconIndex.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxItemRank_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).ItemRank = Convert.ToUInt16(textBoxItemRank.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxGroup_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).group = Convert.ToInt16(textBoxGroup.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxCase_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).itemCase = Convert.ToInt16(textBoxCase.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxGrade_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).Grade = Convert.ToByte(textBoxGrade.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxStr_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).requiredStr = Convert.ToInt16(textBoxStr.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxEss_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).requiredEss = Convert.ToInt16(textBoxEss.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxWis_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).requiredWis = Convert.ToInt16(textBoxWis.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxCon_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).requiredCon = Convert.ToInt16(textBoxCon.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxDex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).requiredDex = Convert.ToInt16(textBoxDex.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void tbKarma_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemAccessory itemAccessory = listBoxAccessories.SelectedItem as ItemAccessory;
			itemAccessory.Fame = Convert.ToByte(tbKarma.Text);
			comboBoxFame.SelectedItem = (FameValues)itemAccessory.Fame;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxPrice_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).Price = Convert.ToUInt32(textBoxPrice.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxCashCheck_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).CashCheck = Convert.ToSByte(textBoxCashCheck.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxTime_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).Time = Convert.ToInt16(textBoxTime.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxMaxSockets_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).maxSocket = Convert.ToByte(textBoxMaxSockets.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxItemSet_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).itemSet = Convert.ToByte(textBoxItemSet.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void checkBoxDrop_CheckedChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).BlockDrop = checkBoxDrop.Checked;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void checkBoxTrade_CheckedChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).BlockTrade = Convert.ToByte(checkBoxTrade.Checked);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void checkBoxNPC_CheckedChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).BlockNpcSell = checkBoxNPC.Checked;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void checkBoxStore_CheckedChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxAccessories.SelectedItem as ItemAccessory).BlockStorage = checkBoxStore.Checked;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void comboBox2ndType_SelectedValueChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory)
			{
				itemAccessory.SecondType = Convert.ToSByte((AccessorySecondType)comboBox2ndType.SelectedItem);
				tbSecondaryType.Text = itemAccessory.SecondType.ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteIndex_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemAccessory.unknownBytes.Count)
			{
				unknownByteValue.Text = itemAccessory.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteValue_TextChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemAccessory.unknownBytes.Count)
			{
				itemAccessory.unknownBytes[unknownByteIndex.SelectedIndex] = Convert.ToByte(unknownByteValue.Text);
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxXSDIndex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemAccessory obj = listBoxAccessories.SelectedItem as ItemAccessory;
			obj.xsdName = Convert.ToUInt16(textBoxXSDIndex.Text);
			obj.updateXsdName();
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btnCopyItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory)
			{
				tempAccessory.Type = itemAccessory.Type;
				tempAccessory.SecondType = itemAccessory.SecondType;
				tempAccessory.ModelIndex = itemAccessory.ModelIndex;
				tempAccessory.IconIndex = itemAccessory.IconIndex;
				tempAccessory.ItemRank = itemAccessory.ItemRank;
				tempAccessory.group = itemAccessory.group;
				tempAccessory.itemCase = itemAccessory.itemCase;
				tempAccessory.Grade = itemAccessory.Grade;
				tempAccessory.xsdName = itemAccessory.xsdName;
				tempAccessory.xsdInfo = itemAccessory.xsdInfo;
				tempAccessory.pack = itemAccessory.pack;
				tempAccessory.clan = itemAccessory.clan;
				tempAccessory.rank = itemAccessory.rank;
				tempAccessory.durability = itemAccessory.durability;
				tempAccessory.Quality = itemAccessory.Quality;
				tempAccessory.Quality2 = itemAccessory.Quality2;
				tempAccessory.requiredLevel = itemAccessory.requiredLevel;
				tempAccessory.requiredDex = itemAccessory.requiredDex;
				tempAccessory.requiredStr = itemAccessory.requiredStr;
				tempAccessory.requiredCon = itemAccessory.requiredCon;
				tempAccessory.requiredEss = itemAccessory.requiredEss;
				tempAccessory.requiredWis = itemAccessory.requiredWis;
				tempAccessory.ApplyClan = itemAccessory.ApplyClan;
				tempAccessory.ClanPoint1 = itemAccessory.ClanPoint1;
				tempAccessory.ClanPoint2 = itemAccessory.ClanPoint2;
				tempAccessory.Price = itemAccessory.Price;
				tempAccessory.maxSocket = itemAccessory.maxSocket;
				tempAccessory.itemSet = itemAccessory.itemSet;
				tempAccessory.BlockDrop = itemAccessory.BlockDrop;
				tempAccessory.BlockTrade = itemAccessory.BlockTrade;
				tempAccessory.BlockNpcSell = itemAccessory.BlockNpcSell;
				tempAccessory.Fame = itemAccessory.Fame;
				tempAccessory.CashCheck = itemAccessory.CashCheck;
				tempAccessory.Time = itemAccessory.Time;
				tempAccessory.BlockStorage = itemAccessory.BlockStorage;
				for (int i = 0; i < 5; i++)
				{
					tempAccessory.effects[i] = itemAccessory.effects[i];
				}
				for (int j = 0; j < 5; j++)
				{
					tempAccessory.specials[j] = itemAccessory.specials[j];
				}
				if (tempAccessory.unknownBytes.Count == 0)
				{
					byte item = 0;
					for (int k = 0; k < itemAccessory.unknownBytes.Count; k++)
					{
						tempAccessory.unknownBytes.Add(item);
					}
				}
				for (int l = 0; l < tempAccessory.unknownBytes.Count; l++)
				{
					itemAccessory.unknownBytes[l] = tempAccessory.unknownBytes[l];
				}
				btnPasteItem.Enabled = true;
			}
			listBoxAccessories_SelectedIndexChanged(this, EventArgs.Empty);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btnPasteItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory)
			{
				itemAccessory.Type = tempAccessory.Type;
				itemAccessory.SecondType = tempAccessory.SecondType;
				itemAccessory.ModelIndex = tempAccessory.ModelIndex;
				itemAccessory.IconIndex = tempAccessory.IconIndex;
				itemAccessory.ItemRank = tempAccessory.ItemRank;
				itemAccessory.group = tempAccessory.group;
				itemAccessory.itemCase = tempAccessory.itemCase;
				itemAccessory.Grade = tempAccessory.Grade;
				itemAccessory.xsdName = tempAccessory.xsdName;
				itemAccessory.xsdInfo = tempAccessory.xsdInfo;
				itemAccessory.pack = tempAccessory.pack;
				itemAccessory.clan = tempAccessory.clan;
				itemAccessory.rank = tempAccessory.rank;
				itemAccessory.durability = tempAccessory.durability;
				itemAccessory.Quality = tempAccessory.Quality;
				itemAccessory.Quality2 = tempAccessory.Quality2;
				itemAccessory.requiredLevel = tempAccessory.requiredLevel;
				itemAccessory.requiredDex = tempAccessory.requiredDex;
				itemAccessory.requiredStr = tempAccessory.requiredStr;
				itemAccessory.requiredCon = tempAccessory.requiredCon;
				itemAccessory.requiredEss = tempAccessory.requiredEss;
				itemAccessory.requiredWis = tempAccessory.requiredWis;
				itemAccessory.ApplyClan = tempAccessory.ApplyClan;
				itemAccessory.ClanPoint1 = tempAccessory.ClanPoint1;
				itemAccessory.ClanPoint2 = tempAccessory.ClanPoint2;
				itemAccessory.Price = tempAccessory.Price;
				itemAccessory.maxSocket = tempAccessory.maxSocket;
				itemAccessory.itemSet = tempAccessory.itemSet;
				itemAccessory.BlockDrop = tempAccessory.BlockDrop;
				itemAccessory.BlockTrade = tempAccessory.BlockTrade;
				itemAccessory.BlockNpcSell = tempAccessory.BlockNpcSell;
				itemAccessory.Fame = tempAccessory.Fame;
				itemAccessory.CashCheck = tempAccessory.CashCheck;
				itemAccessory.Time = tempAccessory.Time;
				itemAccessory.BlockStorage = tempAccessory.BlockStorage;
				for (int i = 0; i < 5; i++)
				{
					itemAccessory.effects[i].ID = tempAccessory.effects[i].ID;
					itemAccessory.effects[i].Type = tempAccessory.effects[i].Type;
					itemAccessory.effects[i].Value = tempAccessory.effects[i].Value;
					itemAccessory.effects[i].Per = tempAccessory.effects[i].Per;
					itemAccessory.effects[i].Prob = tempAccessory.effects[i].Prob;
				}
				for (int j = 0; j < 4; j++)
				{
					itemAccessory.specials[j].ID = tempAccessory.specials[j].ID;
					itemAccessory.specials[j].Type = tempAccessory.specials[j].Type;
					itemAccessory.specials[j].Value = tempAccessory.specials[j].Value;
					itemAccessory.specials[j].Time = tempAccessory.specials[j].Time;
					itemAccessory.specials[j].Prob = tempAccessory.specials[j].Prob;
				}
				for (int k = 0; k < tempAccessory.unknownBytes.Count; k++)
				{
					itemAccessory.unknownBytes[k] = tempAccessory.unknownBytes[k];
				}
			}
			listBoxAccessories_SelectedIndexChanged(this, EventArgs.Empty);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btnCopyEffects_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory)
			{
				for (int i = 0; i < 5; i++)
				{
					tempAccessory2.effects[i] = itemAccessory.effects[i];
				}
				btnPasteEffects.Enabled = true;
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btnPasteEffects_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory)
			{
				for (int i = 0; i < 5; i++)
				{
					itemAccessory.effects[i].ID = tempAccessory2.effects[i].ID;
					itemAccessory.effects[i].Type = tempAccessory2.effects[i].Type;
					itemAccessory.effects[i].Value = tempAccessory2.effects[i].Value;
					itemAccessory.effects[i].Per = tempAccessory2.effects[i].Per;
					itemAccessory.effects[i].Prob = tempAccessory2.effects[i].Prob;
				}
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void comboBox2ndType_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxAccessories.SelectedItem is ItemAccessory itemAccessory)
			{
				itemAccessory.SecondType = Convert.ToSByte((AccessorySecondType)comboBox2ndType.SelectedItem);
				tbSecondaryType.Text = itemAccessory.SecondType.ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            this.listBoxAccessories = new System.Windows.Forms.ListBox();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tableLayoutPanel3 = new System.Windows.Forms.TableLayoutPanel();
            this.label13 = new System.Windows.Forms.Label();
            this.btnIcon = new System.Windows.Forms.Button();
            this.label12 = new System.Windows.Forms.Label();
            this.textBoxName = new System.Windows.Forms.TextBox();
            this.labelType = new System.Windows.Forms.Label();
            this.labelModelIndex = new System.Windows.Forms.Label();
            this.labelIconIndex = new System.Windows.Forms.Label();
            this.labelItemRank = new System.Windows.Forms.Label();
            this.labelGroup = new System.Windows.Forms.Label();
            this.labelCase = new System.Windows.Forms.Label();
            this.labelGrade = new System.Windows.Forms.Label();
            this.textBoxModelIndex = new System.Windows.Forms.TextBox();
            this.textBoxIconIndex = new System.Windows.Forms.TextBox();
            this.textBoxItemRank = new System.Windows.Forms.TextBox();
            this.textBoxGroup = new System.Windows.Forms.TextBox();
            this.textBoxCase = new System.Windows.Forms.TextBox();
            this.textBoxGrade = new System.Windows.Forms.TextBox();
            this.comboBox2ndType = new System.Windows.Forms.ComboBox();
            this.tbSecondaryType = new System.Windows.Forms.TextBox();
            this.unknownByteIndex = new System.Windows.Forms.ComboBox();
            this.unknownByteValue = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.textBoxXSDIndex = new System.Windows.Forms.TextBox();
            this.btnCopyItem = new System.Windows.Forms.Button();
            this.btnPasteEffects = new System.Windows.Forms.Button();
            this.btnPasteItem = new System.Windows.Forms.Button();
            this.btnCopyEffects = new System.Windows.Forms.Button();
            this.tabPageRequirements = new System.Windows.Forms.TabPage();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.labelTxtLv = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.textBoxLevel = new System.Windows.Forms.TextBox();
            this.textBoxStr = new System.Windows.Forms.TextBox();
            this.textBoxEss = new System.Windows.Forms.TextBox();
            this.textBoxWis = new System.Windows.Forms.TextBox();
            this.textBoxCon = new System.Windows.Forms.TextBox();
            this.textBoxDex = new System.Windows.Forms.TextBox();
            this.labelFame = new System.Windows.Forms.Label();
            this.comboBoxFame = new System.Windows.Forms.ComboBox();
            this.tbKarma = new System.Windows.Forms.TextBox();
            this.tabPageDetails = new System.Windows.Forms.TabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.textBoxPrice = new System.Windows.Forms.TextBox();
            this.textBoxCashCheck = new System.Windows.Forms.TextBox();
            this.textBoxTime = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.textBoxItemSet = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.textBoxMaxSockets = new System.Windows.Forms.TextBox();
            this.tabPageEffects = new System.Windows.Forms.TabPage();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.iDDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.valueDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.descriptionDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.nameDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.effectBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.checkBoxStore = new System.Windows.Forms.CheckBox();
            this.checkBoxNPC = new System.Windows.Forms.CheckBox();
            this.checkBoxTrade = new System.Windows.Forms.CheckBox();
            this.checkBoxDrop = new System.Windows.Forms.CheckBox();
            this.tabPageUnknown = new System.Windows.Forms.TabPage();
            this.specialBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tableLayoutPanel3.SuspendLayout();
            this.tabPageRequirements.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.tabPageDetails.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.tabPageEffects.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.effectBindingSource)).BeginInit();
            this.tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.specialBindingSource)).BeginInit();
            this.SuspendLayout();
            // 
            // listBoxAccessories
            // 
            this.listBoxAccessories.Dock = System.Windows.Forms.DockStyle.Left;
            this.listBoxAccessories.FormattingEnabled = true;
            this.listBoxAccessories.ItemHeight = 12;
            this.listBoxAccessories.Location = new System.Drawing.Point(0, 0);
            this.listBoxAccessories.Name = "listBoxAccessories";
            this.listBoxAccessories.Size = new System.Drawing.Size(212, 421);
            this.listBoxAccessories.TabIndex = 0;
            this.listBoxAccessories.SelectedIndexChanged += new System.EventHandler(this.listBoxAccessories_SelectedIndexChanged);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPageRequirements);
            this.tabControl1.Controls.Add(this.tabPageDetails);
            this.tabControl1.Controls.Add(this.tabPageEffects);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPageUnknown);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(212, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(478, 421);
            this.tabControl1.TabIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.tableLayoutPanel3);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(470, 395);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "Basic";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel3
            // 
            this.tableLayoutPanel3.ColumnCount = 3;
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel3.Controls.Add(this.label13, 0, 8);
            this.tableLayoutPanel3.Controls.Add(this.btnIcon, 2, 0);
            this.tableLayoutPanel3.Controls.Add(this.label12, 0, 0);
            this.tableLayoutPanel3.Controls.Add(this.textBoxName, 1, 0);
            this.tableLayoutPanel3.Controls.Add(this.labelType, 0, 1);
            this.tableLayoutPanel3.Controls.Add(this.labelModelIndex, 0, 2);
            this.tableLayoutPanel3.Controls.Add(this.labelIconIndex, 0, 3);
            this.tableLayoutPanel3.Controls.Add(this.labelItemRank, 0, 4);
            this.tableLayoutPanel3.Controls.Add(this.labelGroup, 0, 5);
            this.tableLayoutPanel3.Controls.Add(this.labelCase, 0, 6);
            this.tableLayoutPanel3.Controls.Add(this.labelGrade, 0, 7);
            this.tableLayoutPanel3.Controls.Add(this.textBoxModelIndex, 1, 2);
            this.tableLayoutPanel3.Controls.Add(this.textBoxIconIndex, 1, 3);
            this.tableLayoutPanel3.Controls.Add(this.textBoxItemRank, 1, 4);
            this.tableLayoutPanel3.Controls.Add(this.textBoxGroup, 1, 5);
            this.tableLayoutPanel3.Controls.Add(this.textBoxCase, 1, 6);
            this.tableLayoutPanel3.Controls.Add(this.textBoxGrade, 1, 7);
            this.tableLayoutPanel3.Controls.Add(this.comboBox2ndType, 2, 1);
            this.tableLayoutPanel3.Controls.Add(this.tbSecondaryType, 1, 1);
            this.tableLayoutPanel3.Controls.Add(this.unknownByteIndex, 1, 8);
            this.tableLayoutPanel3.Controls.Add(this.unknownByteValue, 2, 8);
            this.tableLayoutPanel3.Controls.Add(this.label14, 0, 10);
            this.tableLayoutPanel3.Controls.Add(this.textBoxXSDIndex, 1, 10);
            this.tableLayoutPanel3.Controls.Add(this.btnCopyItem, 1, 11);
            this.tableLayoutPanel3.Controls.Add(this.btnPasteEffects, 2, 12);
            this.tableLayoutPanel3.Controls.Add(this.btnPasteItem, 2, 11);
            this.tableLayoutPanel3.Controls.Add(this.btnCopyEffects, 1, 12);
            this.tableLayoutPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel3.Location = new System.Drawing.Point(3, 3);
            this.tableLayoutPanel3.Name = "tableLayoutPanel3";
            this.tableLayoutPanel3.RowCount = 14;
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 18F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 22F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 38F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 7F));
            this.tableLayoutPanel3.Size = new System.Drawing.Size(464, 389);
            this.tableLayoutPanel3.TabIndex = 1;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label13.Location = new System.Drawing.Point(3, 225);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(77, 18);
            this.label13.TabIndex = 21;
            this.label13.Text = "UnknownBytes";
            this.label13.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnIcon
            // 
            this.btnIcon.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnIcon.Location = new System.Drawing.Point(213, 3);
            this.btnIcon.Name = "btnIcon";
            this.btnIcon.Size = new System.Drawing.Size(32, 30);
            this.btnIcon.TabIndex = 0;
            this.btnIcon.UseVisualStyleBackColor = true;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label12.Location = new System.Drawing.Point(3, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(77, 36);
            this.label12.TabIndex = 1;
            this.label12.Text = "Name";
            this.label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxName
            // 
            this.textBoxName.Location = new System.Drawing.Point(86, 3);
            this.textBoxName.Name = "textBoxName";
            this.textBoxName.Size = new System.Drawing.Size(100, 21);
            this.textBoxName.TabIndex = 2;
            // 
            // labelType
            // 
            this.labelType.AutoSize = true;
            this.labelType.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelType.Location = new System.Drawing.Point(3, 36);
            this.labelType.Name = "labelType";
            this.labelType.Size = new System.Drawing.Size(77, 27);
            this.labelType.TabIndex = 3;
            this.labelType.Text = "2nd type";
            this.labelType.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelModelIndex
            // 
            this.labelModelIndex.AutoSize = true;
            this.labelModelIndex.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelModelIndex.Location = new System.Drawing.Point(3, 63);
            this.labelModelIndex.Name = "labelModelIndex";
            this.labelModelIndex.Size = new System.Drawing.Size(77, 27);
            this.labelModelIndex.TabIndex = 5;
            this.labelModelIndex.Text = "Model index";
            this.labelModelIndex.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelIconIndex
            // 
            this.labelIconIndex.AutoSize = true;
            this.labelIconIndex.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelIconIndex.Location = new System.Drawing.Point(3, 90);
            this.labelIconIndex.Name = "labelIconIndex";
            this.labelIconIndex.Size = new System.Drawing.Size(77, 27);
            this.labelIconIndex.TabIndex = 6;
            this.labelIconIndex.Text = "Icon index";
            this.labelIconIndex.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelItemRank
            // 
            this.labelItemRank.AutoSize = true;
            this.labelItemRank.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelItemRank.Location = new System.Drawing.Point(3, 117);
            this.labelItemRank.Name = "labelItemRank";
            this.labelItemRank.Size = new System.Drawing.Size(77, 27);
            this.labelItemRank.TabIndex = 7;
            this.labelItemRank.Text = "Item rank";
            this.labelItemRank.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelGroup
            // 
            this.labelGroup.AutoSize = true;
            this.labelGroup.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelGroup.Location = new System.Drawing.Point(3, 144);
            this.labelGroup.Name = "labelGroup";
            this.labelGroup.Size = new System.Drawing.Size(77, 27);
            this.labelGroup.TabIndex = 8;
            this.labelGroup.Text = "Group";
            this.labelGroup.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelCase
            // 
            this.labelCase.AutoSize = true;
            this.labelCase.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelCase.Location = new System.Drawing.Point(3, 171);
            this.labelCase.Name = "labelCase";
            this.labelCase.Size = new System.Drawing.Size(77, 27);
            this.labelCase.TabIndex = 9;
            this.labelCase.Text = "Case";
            this.labelCase.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelGrade
            // 
            this.labelGrade.AutoSize = true;
            this.labelGrade.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelGrade.Location = new System.Drawing.Point(3, 198);
            this.labelGrade.Name = "labelGrade";
            this.labelGrade.Size = new System.Drawing.Size(77, 27);
            this.labelGrade.TabIndex = 10;
            this.labelGrade.Text = "Grade";
            this.labelGrade.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxModelIndex
            // 
            this.textBoxModelIndex.Location = new System.Drawing.Point(86, 66);
            this.textBoxModelIndex.Name = "textBoxModelIndex";
            this.textBoxModelIndex.Size = new System.Drawing.Size(100, 21);
            this.textBoxModelIndex.TabIndex = 11;
            this.textBoxModelIndex.TextChanged += new System.EventHandler(this.textBoxModelIndex_TextChanged);
            // 
            // textBoxIconIndex
            // 
            this.textBoxIconIndex.Location = new System.Drawing.Point(86, 93);
            this.textBoxIconIndex.Name = "textBoxIconIndex";
            this.textBoxIconIndex.Size = new System.Drawing.Size(100, 21);
            this.textBoxIconIndex.TabIndex = 12;
            this.textBoxIconIndex.TextChanged += new System.EventHandler(this.textBoxIconIndex_TextChanged);
            // 
            // textBoxItemRank
            // 
            this.textBoxItemRank.Location = new System.Drawing.Point(86, 120);
            this.textBoxItemRank.Name = "textBoxItemRank";
            this.textBoxItemRank.Size = new System.Drawing.Size(100, 21);
            this.textBoxItemRank.TabIndex = 13;
            this.textBoxItemRank.TextChanged += new System.EventHandler(this.textBoxItemRank_TextChanged);
            // 
            // textBoxGroup
            // 
            this.textBoxGroup.Location = new System.Drawing.Point(86, 147);
            this.textBoxGroup.Name = "textBoxGroup";
            this.textBoxGroup.Size = new System.Drawing.Size(100, 21);
            this.textBoxGroup.TabIndex = 14;
            this.textBoxGroup.TextChanged += new System.EventHandler(this.textBoxGroup_TextChanged);
            // 
            // textBoxCase
            // 
            this.textBoxCase.Location = new System.Drawing.Point(86, 174);
            this.textBoxCase.Name = "textBoxCase";
            this.textBoxCase.Size = new System.Drawing.Size(100, 21);
            this.textBoxCase.TabIndex = 15;
            this.textBoxCase.TextChanged += new System.EventHandler(this.textBoxCase_TextChanged);
            // 
            // textBoxGrade
            // 
            this.textBoxGrade.Location = new System.Drawing.Point(86, 201);
            this.textBoxGrade.Name = "textBoxGrade";
            this.textBoxGrade.Size = new System.Drawing.Size(100, 21);
            this.textBoxGrade.TabIndex = 16;
            this.textBoxGrade.TextChanged += new System.EventHandler(this.textBoxGrade_TextChanged);
            // 
            // comboBox2ndType
            // 
            this.comboBox2ndType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox2ndType.FormattingEnabled = true;
            this.comboBox2ndType.Location = new System.Drawing.Point(213, 39);
            this.comboBox2ndType.Name = "comboBox2ndType";
            this.comboBox2ndType.Size = new System.Drawing.Size(100, 20);
            this.comboBox2ndType.TabIndex = 4;
            this.comboBox2ndType.SelectedIndexChanged += new System.EventHandler(this.comboBox2ndType_SelectedIndexChanged);
            // 
            // tbSecondaryType
            // 
            this.tbSecondaryType.Location = new System.Drawing.Point(86, 39);
            this.tbSecondaryType.Name = "tbSecondaryType";
            this.tbSecondaryType.Size = new System.Drawing.Size(100, 21);
            this.tbSecondaryType.TabIndex = 17;
            this.tbSecondaryType.TextChanged += new System.EventHandler(this.tbSecondaryType_TextChanged);
            // 
            // unknownByteIndex
            // 
            this.unknownByteIndex.FormattingEnabled = true;
            this.unknownByteIndex.Location = new System.Drawing.Point(86, 228);
            this.unknownByteIndex.Name = "unknownByteIndex";
            this.unknownByteIndex.Size = new System.Drawing.Size(121, 20);
            this.unknownByteIndex.TabIndex = 22;
            // 
            // unknownByteValue
            // 
            this.unknownByteValue.Location = new System.Drawing.Point(213, 228);
            this.unknownByteValue.Name = "unknownByteValue";
            this.unknownByteValue.Size = new System.Drawing.Size(100, 21);
            this.unknownByteValue.TabIndex = 23;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(3, 269);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(65, 12);
            this.label14.TabIndex = 24;
            this.label14.Text = "XSD Index:";
            // 
            // textBoxXSDIndex
            // 
            this.textBoxXSDIndex.Location = new System.Drawing.Point(86, 272);
            this.textBoxXSDIndex.Name = "textBoxXSDIndex";
            this.textBoxXSDIndex.Size = new System.Drawing.Size(100, 21);
            this.textBoxXSDIndex.TabIndex = 26;
            this.textBoxXSDIndex.TextChanged += new System.EventHandler(this.textBoxXSDIndex_TextChanged);
            // 
            // btnCopyItem
            // 
            this.btnCopyItem.Location = new System.Drawing.Point(86, 294);
            this.btnCopyItem.Name = "btnCopyItem";
            this.btnCopyItem.Size = new System.Drawing.Size(75, 21);
            this.btnCopyItem.TabIndex = 30;
            this.btnCopyItem.Text = "CopyItem";
            this.btnCopyItem.UseVisualStyleBackColor = true;
            this.btnCopyItem.Click += new System.EventHandler(this.btnCopyItem_Click);
            // 
            // btnPasteEffects
            // 
            this.btnPasteEffects.Enabled = false;
            this.btnPasteEffects.Location = new System.Drawing.Point(213, 326);
            this.btnPasteEffects.Name = "btnPasteEffects";
            this.btnPasteEffects.Size = new System.Drawing.Size(75, 21);
            this.btnPasteEffects.TabIndex = 33;
            this.btnPasteEffects.Text = "PasteEffects";
            this.btnPasteEffects.UseVisualStyleBackColor = true;
            this.btnPasteEffects.Click += new System.EventHandler(this.btnPasteEffects_Click);
            // 
            // btnPasteItem
            // 
            this.btnPasteItem.Enabled = false;
            this.btnPasteItem.Location = new System.Drawing.Point(213, 294);
            this.btnPasteItem.Name = "btnPasteItem";
            this.btnPasteItem.Size = new System.Drawing.Size(75, 21);
            this.btnPasteItem.TabIndex = 31;
            this.btnPasteItem.Text = "PasteItem";
            this.btnPasteItem.UseVisualStyleBackColor = true;
            this.btnPasteItem.Click += new System.EventHandler(this.btnPasteItem_Click);
            // 
            // btnCopyEffects
            // 
            this.btnCopyEffects.Location = new System.Drawing.Point(86, 326);
            this.btnCopyEffects.Name = "btnCopyEffects";
            this.btnCopyEffects.Size = new System.Drawing.Size(75, 21);
            this.btnCopyEffects.TabIndex = 32;
            this.btnCopyEffects.Text = "CopyEffects";
            this.btnCopyEffects.UseVisualStyleBackColor = true;
            this.btnCopyEffects.Click += new System.EventHandler(this.btnCopyEffects_Click);
            // 
            // tabPageRequirements
            // 
            this.tabPageRequirements.Controls.Add(this.tableLayoutPanel1);
            this.tabPageRequirements.Location = new System.Drawing.Point(4, 22);
            this.tabPageRequirements.Name = "tabPageRequirements";
            this.tabPageRequirements.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageRequirements.Size = new System.Drawing.Size(470, 395);
            this.tabPageRequirements.TabIndex = 2;
            this.tabPageRequirements.Text = "Requirements";
            this.tabPageRequirements.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 3;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel1.Controls.Add(this.label1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.label2, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.labelTxtLv, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.label5, 0, 4);
            this.tableLayoutPanel1.Controls.Add(this.label6, 0, 5);
            this.tableLayoutPanel1.Controls.Add(this.label3, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.label4, 0, 3);
            this.tableLayoutPanel1.Controls.Add(this.textBoxLevel, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.textBoxStr, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.textBoxEss, 1, 2);
            this.tableLayoutPanel1.Controls.Add(this.textBoxWis, 1, 3);
            this.tableLayoutPanel1.Controls.Add(this.textBoxCon, 1, 4);
            this.tableLayoutPanel1.Controls.Add(this.textBoxDex, 1, 5);
            this.tableLayoutPanel1.Controls.Add(this.labelFame, 0, 6);
            this.tableLayoutPanel1.Controls.Add(this.comboBoxFame, 2, 6);
            this.tableLayoutPanel1.Controls.Add(this.tbKarma, 1, 6);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(3, 3);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 8;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 18F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(464, 389);
            this.tableLayoutPanel1.TabIndex = 0;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label1.Location = new System.Drawing.Point(3, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 27);
            this.label1.TabIndex = 0;
            this.label1.Text = "Level";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label2.Location = new System.Drawing.Point(3, 27);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(77, 27);
            this.label2.TabIndex = 1;
            this.label2.Text = "Strength";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // labelTxtLv
            // 
            this.labelTxtLv.AutoSize = true;
            this.labelTxtLv.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelTxtLv.Location = new System.Drawing.Point(192, 0);
            this.labelTxtLv.Name = "labelTxtLv";
            this.labelTxtLv.Size = new System.Drawing.Size(269, 27);
            this.labelTxtLv.TabIndex = 2;
            this.labelTxtLv.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label5.Location = new System.Drawing.Point(3, 108);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(77, 27);
            this.label5.TabIndex = 5;
            this.label5.Text = "Constitution";
            this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label6.Location = new System.Drawing.Point(3, 135);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(77, 27);
            this.label6.TabIndex = 6;
            this.label6.Text = "Dexterity";
            this.label6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label3.Location = new System.Drawing.Point(3, 54);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 27);
            this.label3.TabIndex = 3;
            this.label3.Text = "Essence";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label4.Location = new System.Drawing.Point(3, 81);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(77, 27);
            this.label4.TabIndex = 4;
            this.label4.Text = "Wisdom";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxLevel
            // 
            this.textBoxLevel.Location = new System.Drawing.Point(86, 3);
            this.textBoxLevel.Name = "textBoxLevel";
            this.textBoxLevel.Size = new System.Drawing.Size(100, 21);
            this.textBoxLevel.TabIndex = 7;
            this.textBoxLevel.TextChanged += new System.EventHandler(this.textBoxLevel_TextChanged);
            // 
            // textBoxStr
            // 
            this.textBoxStr.Location = new System.Drawing.Point(86, 30);
            this.textBoxStr.Name = "textBoxStr";
            this.textBoxStr.Size = new System.Drawing.Size(100, 21);
            this.textBoxStr.TabIndex = 8;
            this.textBoxStr.TextChanged += new System.EventHandler(this.textBoxStr_TextChanged);
            // 
            // textBoxEss
            // 
            this.textBoxEss.Location = new System.Drawing.Point(86, 57);
            this.textBoxEss.Name = "textBoxEss";
            this.textBoxEss.Size = new System.Drawing.Size(100, 21);
            this.textBoxEss.TabIndex = 9;
            this.textBoxEss.TextChanged += new System.EventHandler(this.textBoxEss_TextChanged);
            // 
            // textBoxWis
            // 
            this.textBoxWis.Location = new System.Drawing.Point(86, 84);
            this.textBoxWis.Name = "textBoxWis";
            this.textBoxWis.Size = new System.Drawing.Size(100, 21);
            this.textBoxWis.TabIndex = 10;
            this.textBoxWis.TextChanged += new System.EventHandler(this.textBoxWis_TextChanged);
            // 
            // textBoxCon
            // 
            this.textBoxCon.Location = new System.Drawing.Point(86, 111);
            this.textBoxCon.Name = "textBoxCon";
            this.textBoxCon.Size = new System.Drawing.Size(100, 21);
            this.textBoxCon.TabIndex = 11;
            this.textBoxCon.TextChanged += new System.EventHandler(this.textBoxCon_TextChanged);
            // 
            // textBoxDex
            // 
            this.textBoxDex.Location = new System.Drawing.Point(86, 138);
            this.textBoxDex.Name = "textBoxDex";
            this.textBoxDex.Size = new System.Drawing.Size(100, 21);
            this.textBoxDex.TabIndex = 12;
            this.textBoxDex.TextChanged += new System.EventHandler(this.textBoxDex_TextChanged);
            // 
            // labelFame
            // 
            this.labelFame.AutoSize = true;
            this.labelFame.Dock = System.Windows.Forms.DockStyle.Fill;
            this.labelFame.Location = new System.Drawing.Point(3, 162);
            this.labelFame.Name = "labelFame";
            this.labelFame.Size = new System.Drawing.Size(77, 26);
            this.labelFame.TabIndex = 13;
            this.labelFame.Text = "Karma";
            this.labelFame.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // comboBoxFame
            // 
            this.comboBoxFame.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxFame.FormattingEnabled = true;
            this.comboBoxFame.Location = new System.Drawing.Point(192, 165);
            this.comboBoxFame.Name = "comboBoxFame";
            this.comboBoxFame.Size = new System.Drawing.Size(100, 20);
            this.comboBoxFame.TabIndex = 14;
            // 
            // tbKarma
            // 
            this.tbKarma.Location = new System.Drawing.Point(85, 164);
            this.tbKarma.Margin = new System.Windows.Forms.Padding(2);
            this.tbKarma.Name = "tbKarma";
            this.tbKarma.Size = new System.Drawing.Size(101, 21);
            this.tbKarma.TabIndex = 15;
            this.tbKarma.TextChanged += new System.EventHandler(this.tbKarma_TextChanged);
            // 
            // tabPageDetails
            // 
            this.tabPageDetails.Controls.Add(this.tableLayoutPanel2);
            this.tabPageDetails.Location = new System.Drawing.Point(4, 22);
            this.tabPageDetails.Name = "tabPageDetails";
            this.tabPageDetails.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageDetails.Size = new System.Drawing.Size(470, 395);
            this.tabPageDetails.TabIndex = 3;
            this.tabPageDetails.Text = "Details";
            this.tabPageDetails.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 2;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
            this.tableLayoutPanel2.Controls.Add(this.label7, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.label8, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.label9, 0, 2);
            this.tableLayoutPanel2.Controls.Add(this.textBoxPrice, 1, 0);
            this.tableLayoutPanel2.Controls.Add(this.textBoxCashCheck, 1, 1);
            this.tableLayoutPanel2.Controls.Add(this.textBoxTime, 1, 2);
            this.tableLayoutPanel2.Controls.Add(this.label10, 0, 7);
            this.tableLayoutPanel2.Controls.Add(this.textBoxItemSet, 1, 7);
            this.tableLayoutPanel2.Controls.Add(this.label11, 0, 6);
            this.tableLayoutPanel2.Controls.Add(this.textBoxMaxSockets, 1, 6);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(3, 3);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 9;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 18F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(464, 389);
            this.tableLayoutPanel2.TabIndex = 0;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label7.Location = new System.Drawing.Point(3, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(71, 27);
            this.label7.TabIndex = 0;
            this.label7.Text = "Price";
            this.label7.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label8.Location = new System.Drawing.Point(3, 27);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(71, 27);
            this.label8.TabIndex = 1;
            this.label8.Text = "Cash check";
            this.label8.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label9.Location = new System.Drawing.Point(3, 54);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(71, 27);
            this.label9.TabIndex = 2;
            this.label9.Text = "Time";
            this.label9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxPrice
            // 
            this.textBoxPrice.Location = new System.Drawing.Point(80, 3);
            this.textBoxPrice.Name = "textBoxPrice";
            this.textBoxPrice.Size = new System.Drawing.Size(100, 21);
            this.textBoxPrice.TabIndex = 3;
            this.textBoxPrice.TextChanged += new System.EventHandler(this.textBoxPrice_TextChanged);
            // 
            // textBoxCashCheck
            // 
            this.textBoxCashCheck.Location = new System.Drawing.Point(80, 30);
            this.textBoxCashCheck.Name = "textBoxCashCheck";
            this.textBoxCashCheck.Size = new System.Drawing.Size(100, 21);
            this.textBoxCashCheck.TabIndex = 4;
            this.textBoxCashCheck.TextChanged += new System.EventHandler(this.textBoxCashCheck_TextChanged);
            // 
            // textBoxTime
            // 
            this.textBoxTime.Location = new System.Drawing.Point(80, 57);
            this.textBoxTime.Name = "textBoxTime";
            this.textBoxTime.Size = new System.Drawing.Size(100, 21);
            this.textBoxTime.TabIndex = 5;
            this.textBoxTime.TextChanged += new System.EventHandler(this.textBoxTime_TextChanged);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label10.Location = new System.Drawing.Point(3, 108);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(71, 27);
            this.label10.TabIndex = 6;
            this.label10.Text = "Item set";
            this.label10.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxItemSet
            // 
            this.textBoxItemSet.Location = new System.Drawing.Point(80, 111);
            this.textBoxItemSet.Name = "textBoxItemSet";
            this.textBoxItemSet.Size = new System.Drawing.Size(100, 21);
            this.textBoxItemSet.TabIndex = 7;
            this.textBoxItemSet.TextChanged += new System.EventHandler(this.textBoxItemSet_TextChanged);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Dock = System.Windows.Forms.DockStyle.Fill;
            this.label11.Location = new System.Drawing.Point(3, 81);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(71, 27);
            this.label11.TabIndex = 8;
            this.label11.Text = "Max sockets";
            this.label11.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // textBoxMaxSockets
            // 
            this.textBoxMaxSockets.Location = new System.Drawing.Point(80, 84);
            this.textBoxMaxSockets.Name = "textBoxMaxSockets";
            this.textBoxMaxSockets.Size = new System.Drawing.Size(100, 21);
            this.textBoxMaxSockets.TabIndex = 9;
            this.textBoxMaxSockets.TextChanged += new System.EventHandler(this.textBoxMaxSockets_TextChanged);
            // 
            // tabPageEffects
            // 
            this.tabPageEffects.Controls.Add(this.dataGridView1);
            this.tabPageEffects.Location = new System.Drawing.Point(4, 22);
            this.tabPageEffects.Name = "tabPageEffects";
            this.tabPageEffects.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageEffects.Size = new System.Drawing.Size(470, 395);
            this.tabPageEffects.TabIndex = 5;
            this.tabPageEffects.Text = "Effects";
            this.tabPageEffects.UseVisualStyleBackColor = true;
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.AutoGenerateColumns = false;
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.iDDataGridViewTextBoxColumn,
            this.valueDataGridViewTextBoxColumn,
            this.descriptionDataGridViewTextBoxColumn,
            this.nameDataGridViewTextBoxColumn});
            this.dataGridView1.DataSource = this.effectBindingSource;
            this.dataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView1.Location = new System.Drawing.Point(3, 3);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.Size = new System.Drawing.Size(464, 389);
            this.dataGridView1.TabIndex = 0;
            this.dataGridView1.CellValueChanged += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView1_CellValueChanged);
            // 
            // iDDataGridViewTextBoxColumn
            // 
            this.iDDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
            this.iDDataGridViewTextBoxColumn.DataPropertyName = "ID";
            this.iDDataGridViewTextBoxColumn.HeaderText = "ID";
            this.iDDataGridViewTextBoxColumn.Name = "iDDataGridViewTextBoxColumn";
            this.iDDataGridViewTextBoxColumn.Width = 42;
            // 
            // valueDataGridViewTextBoxColumn
            // 
            this.valueDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
            this.valueDataGridViewTextBoxColumn.DataPropertyName = "Value";
            this.valueDataGridViewTextBoxColumn.HeaderText = "Value";
            this.valueDataGridViewTextBoxColumn.Name = "valueDataGridViewTextBoxColumn";
            this.valueDataGridViewTextBoxColumn.Width = 60;
            // 
            // descriptionDataGridViewTextBoxColumn
            // 
            this.descriptionDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
            this.descriptionDataGridViewTextBoxColumn.DataPropertyName = "Description";
            this.descriptionDataGridViewTextBoxColumn.HeaderText = "Description";
            this.descriptionDataGridViewTextBoxColumn.Name = "descriptionDataGridViewTextBoxColumn";
            this.descriptionDataGridViewTextBoxColumn.ReadOnly = true;
            this.descriptionDataGridViewTextBoxColumn.Width = 96;
            // 
            // nameDataGridViewTextBoxColumn
            // 
            this.nameDataGridViewTextBoxColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells;
            this.nameDataGridViewTextBoxColumn.DataPropertyName = "Name";
            this.nameDataGridViewTextBoxColumn.HeaderText = "Name";
            this.nameDataGridViewTextBoxColumn.Name = "nameDataGridViewTextBoxColumn";
            this.nameDataGridViewTextBoxColumn.Width = 54;
            // 
            // effectBindingSource
            // 
            this.effectBindingSource.DataSource = typeof(ItemTableReader.Effect);
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.checkBoxStore);
            this.tabPage2.Controls.Add(this.checkBoxNPC);
            this.tabPage2.Controls.Add(this.checkBoxTrade);
            this.tabPage2.Controls.Add(this.checkBoxDrop);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(470, 395);
            this.tabPage2.TabIndex = 4;
            this.tabPage2.Text = "Additional Options";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // checkBoxStore
            // 
            this.checkBoxStore.AutoSize = true;
            this.checkBoxStore.Location = new System.Drawing.Point(7, 73);
            this.checkBoxStore.Name = "checkBoxStore";
            this.checkBoxStore.Size = new System.Drawing.Size(78, 16);
            this.checkBoxStore.TabIndex = 3;
            this.checkBoxStore.Text = "Can store";
            this.checkBoxStore.UseVisualStyleBackColor = true;
            this.checkBoxStore.CheckedChanged += new System.EventHandler(this.checkBoxStore_CheckedChanged);
            // 
            // checkBoxNPC
            // 
            this.checkBoxNPC.AutoSize = true;
            this.checkBoxNPC.Location = new System.Drawing.Point(7, 51);
            this.checkBoxNPC.Name = "checkBoxNPC";
            this.checkBoxNPC.Size = new System.Drawing.Size(114, 16);
            this.checkBoxNPC.TabIndex = 2;
            this.checkBoxNPC.Text = "Can sell to NPC";
            this.checkBoxNPC.UseVisualStyleBackColor = true;
            this.checkBoxNPC.CheckedChanged += new System.EventHandler(this.checkBoxNPC_CheckedChanged);
            // 
            // checkBoxTrade
            // 
            this.checkBoxTrade.AutoSize = true;
            this.checkBoxTrade.Location = new System.Drawing.Point(7, 29);
            this.checkBoxTrade.Name = "checkBoxTrade";
            this.checkBoxTrade.Size = new System.Drawing.Size(78, 16);
            this.checkBoxTrade.TabIndex = 1;
            this.checkBoxTrade.Text = "Can trade";
            this.checkBoxTrade.UseVisualStyleBackColor = true;
            this.checkBoxTrade.CheckedChanged += new System.EventHandler(this.checkBoxTrade_CheckedChanged);
            // 
            // checkBoxDrop
            // 
            this.checkBoxDrop.AutoSize = true;
            this.checkBoxDrop.Location = new System.Drawing.Point(7, 6);
            this.checkBoxDrop.Name = "checkBoxDrop";
            this.checkBoxDrop.Size = new System.Drawing.Size(72, 16);
            this.checkBoxDrop.TabIndex = 0;
            this.checkBoxDrop.Text = "Can drop";
            this.checkBoxDrop.UseVisualStyleBackColor = true;
            this.checkBoxDrop.CheckedChanged += new System.EventHandler(this.checkBoxDrop_CheckedChanged);
            // 
            // tabPageUnknown
            // 
            this.tabPageUnknown.Location = new System.Drawing.Point(4, 22);
            this.tabPageUnknown.Name = "tabPageUnknown";
            this.tabPageUnknown.Padding = new System.Windows.Forms.Padding(3);
            this.tabPageUnknown.Size = new System.Drawing.Size(470, 395);
            this.tabPageUnknown.TabIndex = 1;
            this.tabPageUnknown.Text = "Unknown bytes";
            this.tabPageUnknown.UseVisualStyleBackColor = true;
            // 
            // specialBindingSource
            // 
            this.specialBindingSource.DataSource = typeof(ItemTableReader.Special);
            // 
            // FormAccessories
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(690, 421);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.listBoxAccessories);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FormAccessories";
            this.ShowIcon = false;
            this.Text = "Accesories Editor";
            this.Load += new System.EventHandler(this.FormAccessories_Load);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.FormAccessories_KeyDown);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tableLayoutPanel3.ResumeLayout(false);
            this.tableLayoutPanel3.PerformLayout();
            this.tabPageRequirements.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            this.tabPageDetails.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.tableLayoutPanel2.PerformLayout();
            this.tabPageEffects.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.effectBindingSource)).EndInit();
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.specialBindingSource)).EndInit();
            this.ResumeLayout(false);

	}
}
