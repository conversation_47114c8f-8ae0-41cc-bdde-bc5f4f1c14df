using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Resources;
using System.Text;
using ItemTableReader.Properties;

namespace ItemTableReader;

internal class ItemParser
{
	public static Map<ushort, List<ItemBase>> ItemRanks = new Map<ushort, List<ItemBase>>();

	public static byte[] LoadString(Stream s)
	{
		BinaryReader binaryReader = new BinaryReader(s);
		int count = binaryReader.ReadInt32();
		return binaryReader.ReadBytes(count);
	}

	public static string Encode(byte[] str, string encoding = "euc-kr")
	{
		return Encoding.GetEncoding(encoding).GetString(str);
	}

	public static Bitmap SetIcon(ItemWeapon weapon)
	{
		int iconIndex = weapon.IconIndex;
		int num = --iconIndex % 8;
		int num2 = iconIndex / 8;
		WeaponSecondType secondType = (WeaponSecondType)weapon.SecondType;
		string name = "";
		ResourceManager resourceManager = Icons.ResourceManager;
		switch (secondType)
		{
		case WeaponSecondType.LONG:
		{
			ThirdTypeWeaponLong thirdType3 = (ThirdTypeWeaponLong)weapon.thirdType;
			name = thirdType3.ToString();
			break;
		}
		case WeaponSecondType.SHORT:
		{
			ThirdTypeWeaponShort thirdType2 = (ThirdTypeWeaponShort)weapon.thirdType;
			name = thirdType2.ToString();
			break;
		}
		case WeaponSecondType.SPECIAL:
		{
			ThirdTypeWeaponSpecial thirdType = (ThirdTypeWeaponSpecial)weapon.thirdType;
			name = thirdType.ToString();
			break;
		}
		}
		Bitmap bitmap = null;
		try
		{
			bitmap = resourceManager.GetObject(name) as Bitmap;
		}
		catch
		{
		}
		if (bitmap != null)
		{
			bitmap = CropImage(bitmap, new Rectangle(num * 32, num2 * 32, 32, 32));
		}
		return bitmap;
	}

	public static Bitmap SetIcon(ItemElixir elixir)
	{
		int num = elixir.IconIndex;
		string text = "";
		if (elixir.ThirdType == 0)
		{
			text = "ELIXIRS";
		}
		else
		{
			text = "SUPPLEMENTS";
			num -= 64;
		}
		int num2 = --num % 8;
		int num3 = num / 8;
		ResourceManager resourceManager = Icons.ResourceManager;
		Bitmap originalImage = null;
		try
		{
			originalImage = resourceManager.GetObject(text) as Bitmap;
		}
		catch
		{
		}
		return CropImage(originalImage, new Rectangle(num2 * 32, num3 * 32, 32, 32));
	}

	public static Bitmap SetIcon(ItemAccessory accessory)
	{
		int iconIndex = accessory.IconIndex;
		string name = $"ACCESSORIES{1 + --iconIndex / 64}";
		iconIndex %= 64;
		int num = iconIndex % 8;
		int num2 = iconIndex / 8;
		ResourceManager resourceManager = Icons.ResourceManager;
		Bitmap originalImage = null;
		try
		{
			originalImage = resourceManager.GetObject(name) as Bitmap;
		}
		catch
		{
		}
		return CropImage(originalImage, new Rectangle(num * 32, num2 * 32, 32, 32));
	}

	public static Bitmap SetIcon(ItemResource resource)
	{
		int iconIndex = resource.IconIndex;
		int resourceFileIndex = 1 + (--iconIndex / 64);
		iconIndex %= 64;
		int num = iconIndex % 8;
		int num2 = iconIndex / 8;
		Bitmap originalImage = null;
		try
		{
			// 尝试从 ResourceIcons 中获取资源
			switch (resourceFileIndex)
			{
				case 1:
					originalImage = ResourceIcons.RESOURCE1;
					break;
				case 2:
					originalImage = ResourceIcons.RESOURCE2;
					break;
				case 3:
					originalImage = ResourceIcons.RESOURCE3;
					break;
				case 4:
					originalImage = ResourceIcons.RESOURCE4;
					break;
				case 5:
					originalImage = ResourceIcons.RESOURCE5;
					break;
				case 6:
					originalImage = ResourceIcons.RESOURCE6;
					break;
				case 7:
					originalImage = ResourceIcons.RESOURCE7;
					break;
				case 8:
					originalImage = ResourceIcons.RESOURCE8;
					break;
				case 9:
					originalImage = ResourceIcons.RESOURCE9;
					break;
				case 10:
					originalImage = ResourceIcons.RESOURCE10;
					break;
				case 11:
					originalImage = ResourceIcons.RESOURCE11;
					break;
				case 12:
					originalImage = ResourceIcons.RESOURCE12;
					break;
				case 13:
					originalImage = ResourceIcons.RESOURCE13;
					break;
				case 14:
					originalImage = ResourceIcons.RESOURCE14;
					break;
				case 15:
					originalImage = ResourceIcons.RESOURCE15;
					break;
				case 16:
					originalImage = ResourceIcons.RESOURCE16;
					break;
				case 17:
					originalImage = ResourceIcons.RESOURCE17;
					break;
				case 18:
					originalImage = ResourceIcons.RESOURCE18;
					break;
				case 19:
					originalImage = ResourceIcons.RESOURCE19;
					break;
				case 20:
					originalImage = ResourceIcons.RESOURCE20;
					break;
				case 21:
					originalImage = ResourceIcons.RESOURCE21;
					break;
				case 22:
					originalImage = ResourceIcons.RESOURCE22;
					break;
				case 23:
					originalImage = ResourceIcons.RESOURCE23;
					break;
				case 24:
					originalImage = ResourceIcons.RESOURCE24;
					break;
				case 25:
					originalImage = ResourceIcons.RESOURCE25;
					break;
				case 26:
					originalImage = ResourceIcons.RESOURCE26;
					break;
				case 27:
					originalImage = ResourceIcons.RESOURCE27;
					break;
				case 28:
					originalImage = ResourceIcons.RESOURCE28;
					break;
				case 29:
					originalImage = ResourceIcons.RESOURCE29;
					break;
				case 30:
					originalImage = ResourceIcons.RESOURCE30;
					break;
				case 31:
					originalImage = ResourceIcons.RESOURCE31;
					break;
				case 32:
					originalImage = ResourceIcons.RESOURCE32;
					break;
				case 33:
					originalImage = ResourceIcons.RESOURCE33;
					break;
				case 34:
					originalImage = ResourceIcons.RESOURCE34;
					break;
				case 35:
					originalImage = ResourceIcons.RESOURCE35;
					break;
				case 36:
					originalImage = ResourceIcons.RESOURCE36;
					break;
				case 37:
					originalImage = ResourceIcons.RESOURCE37;
					break;
				case 38:
					originalImage = ResourceIcons.RESOURCE38;
					break;
				case 39:
					originalImage = ResourceIcons.RESOURCE39;
					break;
				case 40:
					originalImage = ResourceIcons.RESOURCE40;
					break;
				default:
					// 如果超过范围，尝试使用 GetResourceBitmap 方法
					originalImage = ResourceIcons.GetResourceBitmap(resourceFileIndex);
					break;
			}

			if (originalImage == null)
			{
				// 如果没有对应的资源，尝试从 Icons 中获取
				string name = $"RESOURCE{resourceFileIndex}";
				ResourceManager resourceManager = Icons.ResourceManager;
				originalImage = resourceManager.GetObject(name) as Bitmap;
			}

			if (originalImage == null)
			{
				// 尝试使用通用资源图标
				originalImage = ResourceIcons.RESOURCE1;
				System.Diagnostics.Debug.WriteLine($"Using fallback resource icon for ID: {resource.ID}, IconIndex: {iconIndex}, ResourceFile: {resourceFileIndex}");
			}
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"Error loading resource icon: {ex.Message}");
		}
		if (originalImage == null)
		{
			// 创建一个默认图标
			Bitmap defaultIcon = new Bitmap(32, 32);
			using (Graphics g = Graphics.FromImage(defaultIcon))
			{
				// 填充背景
				g.FillRectangle(Brushes.LightGray, 0, 0, 32, 32);
				// 绘制边框
				g.DrawRectangle(Pens.DarkGray, 0, 0, 31, 31);
				// 绘制文字
				String text = "R" + resourceFileIndex;
				Font font = new Font(FontFamily.GenericSansSerif, 12, FontStyle.Bold);
				SizeF size = g.MeasureString(text, font);
				g.DrawString(text, font, Brushes.Black, (32 - size.Width) / 2, (32 - size.Height) / 2);
			}
			System.Diagnostics.Debug.WriteLine($"Created default resource icon for ID: {resource.ID}, ResourceFile: {resourceFileIndex}");
			return defaultIcon;
		}
		return CropImage(originalImage, new Rectangle(num * 32, num2 * 32, 32, 32));
	}

	private static Bitmap CropImage(Image originalImage, Rectangle sourceRectangle, Rectangle? destinationRectangle = null)
	{
		if (!destinationRectangle.HasValue)
		{
			destinationRectangle = new Rectangle(Point.Empty, sourceRectangle.Size);
		}
		Bitmap bitmap = new Bitmap(destinationRectangle.Value.Width, destinationRectangle.Value.Height);
		using Graphics graphics = Graphics.FromImage(bitmap);
		graphics.DrawImage(originalImage, destinationRectangle.Value, sourceRectangle, GraphicsUnit.Pixel);
		return bitmap;
	}
}
