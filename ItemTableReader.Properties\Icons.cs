using System.CodeDom.Compiler;
using System.Collections;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace ItemTableReader.Properties;

[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
[DebuggerNonUserCode]
[CompilerGenerated]
internal class Icons
{
	private static ResourceManager resourceMan;

	private static CultureInfo resourceCulture;

	[EditorBrowsable(EditorBrowsableState.Advanced)]
	internal static ResourceManager ResourceManager
	{
		get
		{
			if (resourceMan == null)
			{
				resourceMan = new ResourceManager("ItemTableReader.Properties.Icons", typeof(Icons).Assembly);
			}
			return resourceMan;
		}
	}

	[EditorBrowsable(EditorBrowsableState.Advanced)]
	internal static CultureInfo Culture
	{
		get
		{
			return resourceCulture;
		}
		set
		{
			resourceCulture = value;
		}
	}

	internal static Bitmap ACCESSORIES1 => (Bitmap)ResourceManager.GetObject("ACCESSORIES1", resourceCulture);

	internal static Bitmap ACCESSORIES2 => (Bitmap)ResourceManager.GetObject("ACCESSORIES2", resourceCulture);

	internal static Bitmap ACCESSORIES3 => (Bitmap)ResourceManager.GetObject("ACCESSORIES3", resourceCulture);

	internal static Bitmap ACCESSORIES4 => (Bitmap)ResourceManager.GetObject("ACCESSORIES4", resourceCulture);

	internal static Bitmap ACCESSORIES5 => (Bitmap)ResourceManager.GetObject("ACCESSORIES5", resourceCulture);

	internal static Bitmap ACCESSORIES6 => (Bitmap)ResourceManager.GetObject("ACCESSORIES6", resourceCulture);

	internal static Bitmap ACCESSORIES7 => (Bitmap)ResourceManager.GetObject("ACCESSORIES7", resourceCulture);
    internal static Bitmap ACCESSORIES8=> (Bitmap)ResourceManager.GetObject("ACCESSORIES8", resourceCulture);
    internal static Bitmap ACCESSORIES9 => (Bitmap)ResourceManager.GetObject("ACCESSORIES9", resourceCulture);
    internal static Bitmap ACCESSORIES10 => (Bitmap)ResourceManager.GetObject("ACCESSORIES10", resourceCulture);
    internal static Bitmap ACCESSORIES11 => (Bitmap)ResourceManager.GetObject("ACCESSORIES11", resourceCulture);
    internal static Bitmap ACCESSORIES12 => (Bitmap)ResourceManager.GetObject("ACCESSORIES12", resourceCulture);
    internal static Bitmap ACCESSORIES13 => (Bitmap)ResourceManager.GetObject("ACCESSORIES13", resourceCulture);
    internal static Bitmap ACCESSORIES14 => (Bitmap)ResourceManager.GetObject("ACCESSORIES14", resourceCulture);


    internal static Bitmap AXE => (Bitmap)ResourceManager.GetObject("AXE", resourceCulture);

	internal static Bitmap BRACERS => (Bitmap)ResourceManager.GetObject("BRACERS", resourceCulture);

	internal static Bitmap DAGGER => (Bitmap)ResourceManager.GetObject("DAGGER", resourceCulture);

	internal static Bitmap ELIXIRS => (Bitmap)ResourceManager.GetObject("ELIXIRS", resourceCulture);

	internal static Bitmap GLOVES => (Bitmap)ResourceManager.GetObject("GLOVES", resourceCulture);

	internal static Bitmap POLEARM => (Bitmap)ResourceManager.GetObject("POLEARM", resourceCulture);

	internal static Bitmap SABER => (Bitmap)ResourceManager.GetObject("SABER", resourceCulture);

	internal static Bitmap SPEAR => (Bitmap)ResourceManager.GetObject("SPEAR", resourceCulture);

	internal static Bitmap STAFF => (Bitmap)ResourceManager.GetObject("STAFF", resourceCulture);

	internal static Bitmap SUPPLEMENTS => (Bitmap)ResourceManager.GetObject("SUPPLEMENTS", resourceCulture);

	internal static Bitmap SWORD => (Bitmap)ResourceManager.GetObject("SWORD", resourceCulture);

	internal static Bitmap WHEELS => (Bitmap)ResourceManager.GetObject("WHEELS", resourceCulture);

	internal static Bitmap RESOURCE1 => (Bitmap)ResourceManager.GetObject("RESOURCE1", resourceCulture);
	internal static Bitmap RESOURCE2 => (Bitmap)ResourceManager.GetObject("RESOURCE2", resourceCulture);
	internal static Bitmap RESOURCE3 => (Bitmap)ResourceManager.GetObject("RESOURCE3", resourceCulture);
	// 根据实际需要添加更多

	internal Icons()
	{
	}

	public static void DebugAvailableResources()
	{
		ResourceManager rm = ResourceManager;
		ResourceSet set = rm.GetResourceSet(CultureInfo.CurrentUICulture, true, true);

		foreach (DictionaryEntry entry in set)
		{
			System.Diagnostics.Debug.WriteLine($"Resource found: {entry.Key}");
		}
	}
}
