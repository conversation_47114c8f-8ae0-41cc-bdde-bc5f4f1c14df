using System;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Windows.Forms;
using ItemTableReader.Properties;

namespace ItemTableReader;

public class FormResources : Form
{
	private static ItemResource tempResource = new ItemResource();

	private IContainer components;

	private ListBox listBoxResources;

	private TabControl tabControl1;

	private TabPage tabPage1;

	private TabPage tabPageUnknown;

	private ByteViewer byteViewer = new ByteViewer();

	private TableLayoutPanel tableLayoutPanel1;

	private Label labelItemRank;

	private TextBox textBoxItemRank;

	private TabPage tabPage2;

	private CheckBox checkBoxStore;

	private CheckBox checkBoxNPC;

	private CheckBox checkBoxTrade;

	private CheckBox checkBoxDrop;

	private Label labelPackage;

	private TextBox textBoxPackage;

	private Label labelType;

	private ComboBox comboBoxType;

	private Label labelCashCheck;

	private TextBox textBoxCashCheck;

	private Label labelTime;

	private TextBox textBoxTime;

	private Label label1;

	private TextBox textBoxTypeNo;

	private Label label2;

	private TextBox textBoxGrade;

	private Label label3;

	private TextBox textBoxPrice;

	private Label label4;

	private ComboBox unknownByteIndex;

	private TextBox unknownByteValue;

	private Button btnPasteItem;

	private Button btnCopyItem;

	private Label label5;

	private Label label6;

	private TextBox textBoxIconIndex;

	private TextBox textBoxXSDIndex;

	private TextBox textBoxXSDInfo;

	private Label label27;

	private Button btnIcon;  // 添加按钮控件声明

	private ToolTip toolTip;  // 添加工具提示控件

	public FormResources()
	{
		InitializeComponent();
		Icons.DebugAvailableResources();  // 添加这行来检查可用资源
		ResourceIcons.DebugAvailableResources();  // 检查资源图标资源

		// 初始化工具提示
		toolTip = new ToolTip();
		toolTip.SetToolTip(btnIcon, "物品图标");

		for (int i = 0; i < 20; i++)
		{
			unknownByteIndex.Items.Add(i.ToString());
		}
	}

	private void FormResources_Load(object sender, EventArgs e)
	{
		listBoxResources.BeginUpdate();
		foreach (ItemResource value in ItemResource.Resources.Values)
		{
			listBoxResources.Items.Add(value);
		}
		listBoxResources.EndUpdate();
		foreach (object value2 in Enum.GetValues(typeof(ResourceSecondType)))
		{
			comboBoxType.Items.Add(value2);
		}
		comboBoxType.ValueMember = "value";
		listBoxResources.DisplayMember = "FullName";
		listBoxResources.ValueMember = "ID";
		tabPageUnknown.Controls.Add(byteViewer);
	}

	private void listBoxResources_SelectedIndexChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource)
		{
			byteViewer.Dock = DockStyle.Fill;
			byteViewer.SetBytes(itemResource.unknownBytes.ToArray());
			textBoxXSDIndex.Text = itemResource.xsdName.ToString();
			textBoxXSDInfo.Text = itemResource.xsdInfo.ToString();
			textBoxIconIndex.Text = itemResource.IconIndex.ToString();
			textBoxItemRank.TextChanged -= textBoxItemRank_TextChanged;
			textBoxCashCheck.TextChanged -= textBoxCashCheck_TextChanged;
			textBoxTime.TextChanged -= textBoxTime_TextChanged;
			textBoxTypeNo.TextChanged -= textBoxTypeNo_TextChanged;
			textBoxGrade.TextChanged -= textBoxGrade_TextChanged;
			textBoxPrice.TextChanged -= textBoxPrice_TextChanged;
			comboBoxType.SelectedValueChanged -= comboBoxType_SelectedValueChanged;
			checkBoxDrop.CheckedChanged -= checkBoxDrop_CheckedChanged;
			checkBoxNPC.CheckedChanged -= checkBoxNPC_CheckedChanged;
			checkBoxTrade.CheckedChanged -= checkBoxTrade_CheckedChanged;
			checkBoxStore.CheckedChanged -= checkBoxStore_CheckedChanged;
			textBoxItemRank.Text = itemResource.ItemRank.ToString();
			textBoxPackage.Text = itemResource.Package.ToString();
			textBoxCashCheck.Text = itemResource.CashCheck.ToString();
			textBoxTime.Text = itemResource.Time.ToString();
			textBoxTypeNo.Text = itemResource.SecondType.ToString();
			textBoxGrade.Text = itemResource.Grade.ToString();
			textBoxPrice.Text = itemResource.Price.ToString();
			comboBoxType.SelectedItem = (ResourceSecondType)itemResource.SecondType;
			checkBoxDrop.Checked = itemResource.BlockDrop;
			checkBoxNPC.Checked = itemResource.BlockNpcSell;
			checkBoxTrade.Checked = Convert.ToBoolean(itemResource.BlockTrade);
			checkBoxStore.Checked = itemResource.BlockStorage;
			textBoxItemRank.TextChanged += textBoxItemRank_TextChanged;
			textBoxCashCheck.TextChanged += textBoxCashCheck_TextChanged;
			textBoxTime.TextChanged += textBoxTime_TextChanged;
			textBoxTypeNo.TextChanged += textBoxTypeNo_TextChanged;
			textBoxGrade.TextChanged += textBoxGrade_TextChanged;
			textBoxPrice.TextChanged += textBoxPrice_TextChanged;
			comboBoxType.SelectedValueChanged += comboBoxType_SelectedValueChanged;
			checkBoxDrop.CheckedChanged += checkBoxDrop_CheckedChanged;
			checkBoxNPC.CheckedChanged += checkBoxNPC_CheckedChanged;
			checkBoxTrade.CheckedChanged += checkBoxTrade_CheckedChanged;
			checkBoxStore.CheckedChanged += checkBoxStore_CheckedChanged;
			if (unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemResource.unknownBytes.Count)
			{
				unknownByteValue.Text = itemResource.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
			}
			// 更新图标显示
			try
			{
				Bitmap icon = ItemParser.SetIcon(itemResource);
				if (icon != null)
				{
					btnIcon.Image = icon;
					btnIcon.BackColor = Color.Transparent;
				}
				else
				{
					btnIcon.Image = null;
					btnIcon.BackColor = Color.LightGray;
					System.Diagnostics.Debug.WriteLine($"No icon found for resource ID: {itemResource.ID}, IconIndex: {itemResource.IconIndex}");
				}
			}
			catch (Exception ex)
			{
				System.Diagnostics.Debug.WriteLine($"Error displaying icon: {ex.Message}");
				btnIcon.Image = null;
				btnIcon.BackColor = Color.Red;
			}
		}
	}

	private void FormResources_KeyDown(object sender, KeyEventArgs e)
	{
		if (e.KeyCode == Keys.Up)
		{
			if (listBoxResources.SelectedIndex > 0)
			{
				listBoxResources.SelectedIndex--;
			}
		}
		else if (e.KeyCode == Keys.Down && listBoxResources.SelectedIndex < listBoxResources.Items.Count - 1)
		{
			listBoxResources.SelectedIndex++;
		}
	}

	private void textBoxPrice_TextChanged(object sender, EventArgs e)
	{
		try
		{
			(listBoxResources.SelectedItem as ItemResource).Price = Convert.ToUInt16(textBoxPrice.Text);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxItemRank_TextChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource && ushort.TryParse(textBoxItemRank.Text, out var result))
		{
			itemResource.ItemRank = result;
		}
	}

	private void comboBoxType_SelectedValueChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource)
		{
			itemResource.SecondType = (sbyte)(ResourceSecondType)comboBoxType.SelectedItem;
		}
	}

	private void textBoxTime_TextChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource && short.TryParse(textBoxTime.Text, out var result))
		{
			itemResource.Time = result;
		}
	}

	private void textBoxCashCheck_TextChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource && sbyte.TryParse(textBoxCashCheck.Text, out var result))
		{
			itemResource.CashCheck = result;
		}
	}

	private void textBoxTypeNo_TextChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource && sbyte.TryParse(textBoxTypeNo.Text, out var result))
		{
			itemResource.SecondType = result;
		}
	}

	private void textBoxGrade_TextChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource && byte.TryParse(textBoxGrade.Text, out var result))
		{
			itemResource.Grade = result;
		}
	}

	private void checkBoxDrop_CheckedChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource)
		{
			itemResource.BlockDrop = checkBoxDrop.Checked;
		}
	}

	private void checkBoxTrade_CheckedChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource)
		{
			itemResource.BlockTrade = Convert.ToByte(checkBoxTrade.Checked);
		}
	}

	private void checkBoxNPC_CheckedChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource)
		{
			itemResource.BlockNpcSell = checkBoxNPC.Checked;
		}
	}

	private void checkBoxStore_CheckedChanged(object sender, EventArgs e)
	{
		if (listBoxResources.SelectedItem is ItemResource itemResource)
		{
			itemResource.BlockStorage = checkBoxStore.Checked;
		}
	}

	private void unknownByteIndex_SelectedIndexChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxResources.SelectedItem is ItemResource itemResource && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemResource.unknownBytes.Count)
			{
				unknownByteValue.Text = itemResource.unknownBytes[unknownByteIndex.SelectedIndex].ToString();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void unknownByteValue_TextChanged(object sender, EventArgs e)
	{
		try
		{
			if (listBoxResources.SelectedItem is ItemResource itemResource && unknownByteIndex.SelectedIndex >= 0 && unknownByteIndex.SelectedIndex < itemResource.unknownBytes.Count)
			{
				itemResource.unknownBytes[unknownByteIndex.SelectedIndex] = Convert.ToByte(unknownByteValue.Text);
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxXSDIndex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemResource obj = listBoxResources.SelectedItem as ItemResource;
			obj.xsdName = Convert.ToUInt16(textBoxXSDIndex.Text);
			obj.updateXsdName();
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxIconIndex_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemResource resource = listBoxResources.SelectedItem as ItemResource;
			if (resource != null)
			{
				resource.IconIndex = Convert.ToInt16(textBoxIconIndex.Text);
				// 更新图标显示
				Bitmap icon = ItemParser.SetIcon(resource);
				if (icon != null)
				{
					btnIcon.Image = icon;
					btnIcon.BackColor = Color.Transparent;
				}
				else
				{
					btnIcon.Image = null;
					btnIcon.BackColor = Color.LightGray;
				}
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
			btnIcon.Image = null;
			btnIcon.BackColor = Color.Red;
		}
	}

	private void btnCopyItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (!(listBoxResources.SelectedItem is ItemResource itemResource))
			{
				return;
			}
			tempResource.Type = itemResource.Type;
			tempResource.SecondType = itemResource.SecondType;
			tempResource.ModelIndex = itemResource.ModelIndex;
			tempResource.Quality = itemResource.Quality;
			tempResource.ApplyClan = itemResource.ApplyClan;
			tempResource.Quality2 = itemResource.Quality2;
			tempResource.IconIndex = itemResource.IconIndex;
			tempResource.packageNumber = itemResource.packageNumber;
			tempResource.ClanPoint1 = itemResource.ClanPoint1;
			tempResource.ClanPoint2 = itemResource.ClanPoint2;
			tempResource.Grade = itemResource.Grade;
			tempResource.Price = itemResource.Price;
			tempResource.ItemRank = itemResource.ItemRank;
			tempResource.BlockDrop = itemResource.BlockDrop;
			tempResource.BlockTrade = itemResource.BlockTrade;
			tempResource.BlockNpcSell = itemResource.BlockNpcSell;
			tempResource.xsdName = itemResource.xsdName;
			tempResource.xsdInfo = itemResource.xsdInfo;
			tempResource.CashCheck = itemResource.CashCheck;
			tempResource.Time = itemResource.Time;
			tempResource.BlockStorage = itemResource.BlockStorage;
			if (tempResource.unknownBytes.Count == 0)
			{
				byte item = 0;
				for (int i = 0; i < itemResource.unknownBytes.Count; i++)
				{
					tempResource.unknownBytes.Add(item);
				}
			}
			for (int j = 0; j < itemResource.unknownBytes.Count; j++)
			{
				tempResource.unknownBytes[j] = itemResource.unknownBytes[j];
			}
			btnPasteItem.Enabled = true;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void btnPasteItem_Click(object sender, EventArgs e)
	{
		try
		{
			if (listBoxResources.SelectedItem is ItemResource itemResource)
			{
				itemResource.Type = tempResource.Type;
				itemResource.SecondType = tempResource.SecondType;
				itemResource.ModelIndex = tempResource.ModelIndex;
				itemResource.Quality = tempResource.Quality;
				itemResource.ApplyClan = tempResource.ApplyClan;
				itemResource.Quality2 = tempResource.Quality2;
				itemResource.IconIndex = tempResource.IconIndex;
				itemResource.packageNumber = tempResource.packageNumber;
				itemResource.ClanPoint1 = tempResource.ClanPoint1;
				itemResource.ClanPoint2 = tempResource.ClanPoint2;
				itemResource.Grade = tempResource.Grade;
				itemResource.Price = tempResource.Price;
				itemResource.ItemRank = tempResource.ItemRank;
				itemResource.BlockDrop = tempResource.BlockDrop;
				itemResource.BlockTrade = tempResource.BlockTrade;
				itemResource.BlockNpcSell = tempResource.BlockNpcSell;
				itemResource.xsdName = tempResource.xsdName;
				itemResource.xsdInfo = tempResource.xsdInfo;
				itemResource.CashCheck = tempResource.CashCheck;
				itemResource.Time = tempResource.Time;
				itemResource.BlockStorage = tempResource.BlockStorage;
				for (int i = 0; i < tempResource.unknownBytes.Count; i++)
				{
					itemResource.unknownBytes[i] = tempResource.unknownBytes[i];
				}
			}
			listBoxResources_SelectedIndexChanged(this, EventArgs.Empty);
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void textBoxXSDInfo_TextChanged(object sender, EventArgs e)
	{
		try
		{
			ItemResource obj = listBoxResources.SelectedItem as ItemResource;
			obj.xsdInfo = Convert.ToUInt16(textBoxXSDInfo.Text);
			obj.updateXsdInfo();
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.listBoxResources = new System.Windows.Forms.ListBox();
		this.tabControl1 = new System.Windows.Forms.TabControl();
		this.tabPage1 = new System.Windows.Forms.TabPage();
		this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
		this.label4 = new System.Windows.Forms.Label();
		this.label3 = new System.Windows.Forms.Label();
		this.labelItemRank = new System.Windows.Forms.Label();
		this.textBoxItemRank = new System.Windows.Forms.TextBox();
		this.labelPackage = new System.Windows.Forms.Label();
		this.textBoxPackage = new System.Windows.Forms.TextBox();
		this.labelType = new System.Windows.Forms.Label();
		this.comboBoxType = new System.Windows.Forms.ComboBox();
		this.labelCashCheck = new System.Windows.Forms.Label();
		this.textBoxCashCheck = new System.Windows.Forms.TextBox();
		this.labelTime = new System.Windows.Forms.Label();
		this.textBoxTime = new System.Windows.Forms.TextBox();
		this.label1 = new System.Windows.Forms.Label();
		this.textBoxTypeNo = new System.Windows.Forms.TextBox();
		this.label2 = new System.Windows.Forms.Label();
		this.textBoxGrade = new System.Windows.Forms.TextBox();
		this.textBoxPrice = new System.Windows.Forms.TextBox();
		this.unknownByteIndex = new System.Windows.Forms.ComboBox();
		this.unknownByteValue = new System.Windows.Forms.TextBox();
		this.label5 = new System.Windows.Forms.Label();
		this.label6 = new System.Windows.Forms.Label();
		this.textBoxIconIndex = new System.Windows.Forms.TextBox();
		this.textBoxXSDIndex = new System.Windows.Forms.TextBox();
		this.btnCopyItem = new System.Windows.Forms.Button();
		this.btnPasteItem = new System.Windows.Forms.Button();
		this.textBoxXSDInfo = new System.Windows.Forms.TextBox();
		this.label27 = new System.Windows.Forms.Label();
		this.tabPage2 = new System.Windows.Forms.TabPage();
		this.checkBoxStore = new System.Windows.Forms.CheckBox();
		this.checkBoxNPC = new System.Windows.Forms.CheckBox();
		this.checkBoxTrade = new System.Windows.Forms.CheckBox();
		this.checkBoxDrop = new System.Windows.Forms.CheckBox();
		this.tabPageUnknown = new System.Windows.Forms.TabPage();
		this.tabControl1.SuspendLayout();
		this.tabPage1.SuspendLayout();
		this.tableLayoutPanel1.SuspendLayout();
		this.tabPage2.SuspendLayout();
		base.SuspendLayout();
		this.listBoxResources.Dock = System.Windows.Forms.DockStyle.Left;
		this.listBoxResources.FormattingEnabled = true;
		this.listBoxResources.Location = new System.Drawing.Point(0, 0);
		this.listBoxResources.Name = "listBoxResources";
		this.listBoxResources.Size = new System.Drawing.Size(233, 457);
		this.listBoxResources.TabIndex = 0;
		this.listBoxResources.SelectedIndexChanged += new System.EventHandler(listBoxResources_SelectedIndexChanged);
		this.tabControl1.Controls.Add(this.tabPage1);
		this.tabControl1.Controls.Add(this.tabPage2);
		this.tabControl1.Controls.Add(this.tabPageUnknown);
		this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tabControl1.Location = new System.Drawing.Point(233, 0);
		this.tabControl1.Name = "tabControl1";
		this.tabControl1.SelectedIndex = 0;
		this.tabControl1.Size = new System.Drawing.Size(389, 457);
		this.tabControl1.TabIndex = 0;
		this.tabPage1.Controls.Add(this.tableLayoutPanel1);
		this.tabPage1.Location = new System.Drawing.Point(4, 22);
		this.tabPage1.Name = "tabPage1";
		this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage1.Size = new System.Drawing.Size(381, 431);
		this.tabPage1.TabIndex = 0;
		this.tabPage1.Text = "Details";
		this.tabPage1.UseVisualStyleBackColor = true;
		this.tableLayoutPanel1.ColumnCount = 3;
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle());
		this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 175f));
		this.tableLayoutPanel1.Controls.Add(this.label4, 0, 10);
		this.tableLayoutPanel1.Controls.Add(this.label3, 0, 9);
		this.tableLayoutPanel1.Controls.Add(this.labelItemRank, 0, 0);
		this.tableLayoutPanel1.Controls.Add(this.textBoxItemRank, 1, 0);
		this.tableLayoutPanel1.Controls.Add(this.labelPackage, 0, 1);
		this.tableLayoutPanel1.Controls.Add(this.textBoxPackage, 1, 1);
		this.tableLayoutPanel1.Controls.Add(this.labelType, 0, 2);
		this.tableLayoutPanel1.Controls.Add(this.comboBoxType, 1, 2);
		this.tableLayoutPanel1.Controls.Add(this.labelCashCheck, 0, 6);
		this.tableLayoutPanel1.Controls.Add(this.textBoxCashCheck, 1, 6);
		this.tableLayoutPanel1.Controls.Add(this.labelTime, 0, 5);
		this.tableLayoutPanel1.Controls.Add(this.textBoxTime, 1, 5);
		this.tableLayoutPanel1.Controls.Add(this.label1, 0, 7);
		this.tableLayoutPanel1.Controls.Add(this.textBoxTypeNo, 1, 7);
		this.tableLayoutPanel1.Controls.Add(this.label2, 0, 8);
		this.tableLayoutPanel1.Controls.Add(this.textBoxGrade, 1, 8);
		this.tableLayoutPanel1.Controls.Add(this.textBoxPrice, 1, 9);
		this.tableLayoutPanel1.Controls.Add(this.unknownByteIndex, 1, 10);
		this.tableLayoutPanel1.Controls.Add(this.unknownByteValue, 2, 10);
		this.tableLayoutPanel1.Controls.Add(this.label5, 0, 12);
		this.tableLayoutPanel1.Controls.Add(this.label6, 0, 11);
		this.tableLayoutPanel1.Controls.Add(this.textBoxIconIndex, 1, 11);
		this.tableLayoutPanel1.Controls.Add(this.textBoxXSDIndex, 1, 12);
		this.tableLayoutPanel1.Controls.Add(this.btnCopyItem, 1, 14);
		this.tableLayoutPanel1.Controls.Add(this.btnPasteItem, 2, 14);
		this.tableLayoutPanel1.Controls.Add(this.textBoxXSDInfo, 1, 13);
		this.tableLayoutPanel1.Controls.Add(this.label27, 0, 13);
		this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.tableLayoutPanel1.Location = new System.Drawing.Point(3, 3);
		this.tableLayoutPanel1.Name = "tableLayoutPanel1";
		this.tableLayoutPanel1.RowCount = 17;
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle());
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 38f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35f));
		this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20f));
		this.tableLayoutPanel1.Size = new System.Drawing.Size(375, 425);
		this.tableLayoutPanel1.TabIndex = 0;
		this.label4.AutoSize = true;
		this.label4.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label4.Location = new System.Drawing.Point(3, 209);
		this.label4.Name = "label4";
		this.label4.Size = new System.Drawing.Size(88, 27);
		this.label4.TabIndex = 16;
		this.label4.Text = "UnkknownBytes";
		this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.label3.AutoSize = true;
		this.label3.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label3.Location = new System.Drawing.Point(3, 183);
		this.label3.Name = "label3";
		this.label3.Size = new System.Drawing.Size(88, 26);
		this.label3.TabIndex = 15;
		this.label3.Text = "Price";
		this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.labelItemRank.AutoSize = true;
		this.labelItemRank.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelItemRank.Location = new System.Drawing.Point(3, 0);
		this.labelItemRank.Name = "labelItemRank";
		this.labelItemRank.Size = new System.Drawing.Size(88, 26);
		this.labelItemRank.TabIndex = 0;
		this.labelItemRank.Text = "Item rank";
		this.labelItemRank.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxItemRank.Location = new System.Drawing.Point(97, 3);
		this.textBoxItemRank.Name = "textBoxItemRank";
		this.textBoxItemRank.Size = new System.Drawing.Size(100, 20);
		this.textBoxItemRank.TabIndex = 1;
		this.textBoxItemRank.TextChanged += new System.EventHandler(textBoxItemRank_TextChanged);
		this.labelPackage.AutoSize = true;
		this.labelPackage.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelPackage.Location = new System.Drawing.Point(3, 26);
		this.labelPackage.Name = "labelPackage";
		this.labelPackage.Size = new System.Drawing.Size(88, 26);
		this.labelPackage.TabIndex = 2;
		this.labelPackage.Text = "Package number";
		this.labelPackage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxPackage.Location = new System.Drawing.Point(97, 29);
		this.textBoxPackage.Name = "textBoxPackage";
		this.textBoxPackage.ReadOnly = true;
		this.textBoxPackage.Size = new System.Drawing.Size(100, 20);
		this.textBoxPackage.TabIndex = 3;
		this.labelType.AutoSize = true;
		this.labelType.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelType.Location = new System.Drawing.Point(3, 52);
		this.labelType.Name = "labelType";
		this.labelType.Size = new System.Drawing.Size(88, 27);
		this.labelType.TabIndex = 4;
		this.labelType.Text = "Type";
		this.labelType.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.comboBoxType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
		this.comboBoxType.FormattingEnabled = true;
		this.comboBoxType.Location = new System.Drawing.Point(97, 55);
		this.comboBoxType.Name = "comboBoxType";
		this.comboBoxType.Size = new System.Drawing.Size(100, 21);
		this.comboBoxType.TabIndex = 5;
		this.comboBoxType.SelectedValueChanged += new System.EventHandler(comboBoxType_SelectedValueChanged);
		this.labelCashCheck.AutoSize = true;
		this.labelCashCheck.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelCashCheck.Location = new System.Drawing.Point(3, 105);
		this.labelCashCheck.Name = "labelCashCheck";
		this.labelCashCheck.Size = new System.Drawing.Size(88, 26);
		this.labelCashCheck.TabIndex = 6;
		this.labelCashCheck.Text = "Cash check";
		this.labelCashCheck.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxCashCheck.Location = new System.Drawing.Point(97, 108);
		this.textBoxCashCheck.Name = "textBoxCashCheck";
		this.textBoxCashCheck.Size = new System.Drawing.Size(100, 20);
		this.textBoxCashCheck.TabIndex = 7;
		this.textBoxCashCheck.TextChanged += new System.EventHandler(textBoxCashCheck_TextChanged);
		this.labelTime.AutoSize = true;
		this.labelTime.Dock = System.Windows.Forms.DockStyle.Fill;
		this.labelTime.Location = new System.Drawing.Point(3, 79);
		this.labelTime.Name = "labelTime";
		this.labelTime.Size = new System.Drawing.Size(88, 26);
		this.labelTime.TabIndex = 8;
		this.labelTime.Text = "Time";
		this.labelTime.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxTime.Location = new System.Drawing.Point(97, 82);
		this.textBoxTime.Name = "textBoxTime";
		this.textBoxTime.Size = new System.Drawing.Size(100, 20);
		this.textBoxTime.TabIndex = 9;
		this.textBoxTime.TextChanged += new System.EventHandler(textBoxTime_TextChanged);
		this.label1.AutoSize = true;
		this.label1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label1.Location = new System.Drawing.Point(3, 131);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(88, 26);
		this.label1.TabIndex = 10;
		this.label1.Text = "TypeNo.";
		this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxTypeNo.Location = new System.Drawing.Point(97, 134);
		this.textBoxTypeNo.Name = "textBoxTypeNo";
		this.textBoxTypeNo.Size = new System.Drawing.Size(100, 20);
		this.textBoxTypeNo.TabIndex = 11;
		this.textBoxTypeNo.TextChanged += new System.EventHandler(textBoxTypeNo_TextChanged);
		this.label2.AutoSize = true;
		this.label2.Dock = System.Windows.Forms.DockStyle.Fill;
		this.label2.Location = new System.Drawing.Point(3, 157);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(88, 26);
		this.label2.TabIndex = 12;
		this.label2.Text = "Grade";
		this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.textBoxGrade.Location = new System.Drawing.Point(97, 160);
		this.textBoxGrade.Name = "textBoxGrade";
		this.textBoxGrade.Size = new System.Drawing.Size(100, 20);
		this.textBoxGrade.TabIndex = 13;
		this.textBoxGrade.TextChanged += new System.EventHandler(textBoxGrade_TextChanged);
		this.textBoxPrice.Location = new System.Drawing.Point(97, 186);
		this.textBoxPrice.Name = "textBoxPrice";
		this.textBoxPrice.Size = new System.Drawing.Size(100, 20);
		this.textBoxPrice.TabIndex = 14;
		this.textBoxPrice.TextChanged += new System.EventHandler(textBoxPrice_TextChanged);
		this.unknownByteIndex.FormattingEnabled = true;
		this.unknownByteIndex.Location = new System.Drawing.Point(97, 212);
		this.unknownByteIndex.Name = "unknownByteIndex";
		this.unknownByteIndex.Size = new System.Drawing.Size(121, 21);
		this.unknownByteIndex.TabIndex = 17;
		this.unknownByteIndex.SelectedIndexChanged += new System.EventHandler(unknownByteIndex_SelectedIndexChanged);
		this.unknownByteValue.Location = new System.Drawing.Point(224, 212);
		this.unknownByteValue.Name = "unknownByteValue";
		this.unknownByteValue.Size = new System.Drawing.Size(100, 20);
		this.unknownByteValue.TabIndex = 18;
		this.unknownByteValue.TextChanged += new System.EventHandler(unknownByteValue_TextChanged);
		this.label5.AutoSize = true;
		this.label5.Location = new System.Drawing.Point(3, 266);
		this.label5.Name = "label5";
		this.label5.Size = new System.Drawing.Size(55, 13);
		this.label5.TabIndex = 26;
		this.label5.Text = "XSDIndex";
		this.label6.AutoSize = true;
		this.label6.Location = new System.Drawing.Point(3, 236);
		this.label6.Name = "label6";
		this.label6.Size = new System.Drawing.Size(54, 13);
		this.label6.TabIndex = 28;
		this.label6.Text = "IconIndex";
		this.textBoxIconIndex.Location = new System.Drawing.Point(97, 239);
		this.textBoxIconIndex.Name = "textBoxIconIndex";
		this.textBoxIconIndex.Size = new System.Drawing.Size(100, 20);
		this.textBoxIconIndex.TabIndex = 29;
		this.textBoxIconIndex.TextChanged += new System.EventHandler(textBoxIconIndex_TextChanged);
		this.textBoxXSDIndex.Location = new System.Drawing.Point(97, 269);
		this.textBoxXSDIndex.Name = "textBoxXSDIndex";
		this.textBoxXSDIndex.Size = new System.Drawing.Size(100, 20);
		this.textBoxXSDIndex.TabIndex = 27;
		this.textBoxXSDIndex.TextChanged += new System.EventHandler(textBoxXSDIndex_TextChanged);
		this.btnCopyItem.Location = new System.Drawing.Point(97, 334);
		this.btnCopyItem.Name = "btnCopyItem";
		this.btnCopyItem.Size = new System.Drawing.Size(75, 23);
		this.btnCopyItem.TabIndex = 30;
		this.btnCopyItem.Text = "CopyItem";
		this.btnCopyItem.UseVisualStyleBackColor = true;
		this.btnCopyItem.Click += new System.EventHandler(btnCopyItem_Click);
		this.btnPasteItem.Enabled = false;
		this.btnPasteItem.Location = new System.Drawing.Point(224, 334);
		this.btnPasteItem.Name = "btnPasteItem";
		this.btnPasteItem.Size = new System.Drawing.Size(75, 23);
		this.btnPasteItem.TabIndex = 31;
		this.btnPasteItem.Text = "PasteItem";
		this.btnPasteItem.UseVisualStyleBackColor = true;
		this.btnPasteItem.Click += new System.EventHandler(btnPasteItem_Click);
		this.textBoxXSDInfo.Location = new System.Drawing.Point(96, 298);
		this.textBoxXSDInfo.Margin = new System.Windows.Forms.Padding(2);
		this.textBoxXSDInfo.Name = "textBoxXSDInfo";
		this.textBoxXSDInfo.Size = new System.Drawing.Size(76, 20);
		this.textBoxXSDInfo.TabIndex = 69;
		this.textBoxXSDInfo.TextChanged += new System.EventHandler(textBoxXSDInfo_TextChanged);
		this.label27.AutoSize = true;
		this.label27.Location = new System.Drawing.Point(2, 296);
		this.label27.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label27.Name = "label27";
		this.label27.Size = new System.Drawing.Size(50, 13);
		this.label27.TabIndex = 68;
		this.label27.Text = "XSD Info";
		this.tabPage2.Controls.Add(this.checkBoxStore);
		this.tabPage2.Controls.Add(this.checkBoxNPC);
		this.tabPage2.Controls.Add(this.checkBoxTrade);
		this.tabPage2.Controls.Add(this.checkBoxDrop);
		this.tabPage2.Location = new System.Drawing.Point(4, 22);
		this.tabPage2.Name = "tabPage2";
		this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage2.Size = new System.Drawing.Size(381, 431);
		this.tabPage2.TabIndex = 2;
		this.tabPage2.Text = "Additional Options";
		this.tabPage2.UseVisualStyleBackColor = true;
		this.checkBoxStore.AutoSize = true;
		this.checkBoxStore.Location = new System.Drawing.Point(6, 78);
		this.checkBoxStore.Name = "checkBoxStore";
		this.checkBoxStore.Size = new System.Drawing.Size(76, 17);
		this.checkBoxStore.TabIndex = 3;
		this.checkBoxStore.Text = "Can't store";
		this.checkBoxStore.UseVisualStyleBackColor = true;
		this.checkBoxStore.CheckedChanged += new System.EventHandler(checkBoxStore_CheckedChanged);
		this.checkBoxNPC.AutoSize = true;
		this.checkBoxNPC.Location = new System.Drawing.Point(6, 54);
		this.checkBoxNPC.Name = "checkBoxNPC";
		this.checkBoxNPC.Size = new System.Drawing.Size(105, 17);
		this.checkBoxNPC.TabIndex = 2;
		this.checkBoxNPC.Text = "Can't sell to NPC";
		this.checkBoxNPC.UseVisualStyleBackColor = true;
		this.checkBoxNPC.CheckedChanged += new System.EventHandler(checkBoxNPC_CheckedChanged);
		this.checkBoxTrade.AutoSize = true;
		this.checkBoxTrade.Location = new System.Drawing.Point(6, 30);
		this.checkBoxTrade.Name = "checkBoxTrade";
		this.checkBoxTrade.Size = new System.Drawing.Size(77, 17);
		this.checkBoxTrade.TabIndex = 1;
		this.checkBoxTrade.Text = "Can't trade";
		this.checkBoxTrade.UseVisualStyleBackColor = true;
		this.checkBoxTrade.CheckedChanged += new System.EventHandler(checkBoxTrade_CheckedChanged);
		this.checkBoxDrop.AutoSize = true;
		this.checkBoxDrop.Location = new System.Drawing.Point(6, 6);
		this.checkBoxDrop.Name = "checkBoxDrop";
		this.checkBoxDrop.Size = new System.Drawing.Size(74, 17);
		this.checkBoxDrop.TabIndex = 0;
		this.checkBoxDrop.Text = "Can't drop";
		this.checkBoxDrop.UseVisualStyleBackColor = true;
		this.checkBoxDrop.CheckedChanged += new System.EventHandler(checkBoxDrop_CheckedChanged);
		this.tabPageUnknown.Location = new System.Drawing.Point(4, 22);
		this.tabPageUnknown.Name = "tabPageUnknown";
		this.tabPageUnknown.Padding = new System.Windows.Forms.Padding(3);
		this.tabPageUnknown.Size = new System.Drawing.Size(381, 431);
		this.tabPageUnknown.TabIndex = 1;
		this.tabPageUnknown.Text = "Unknown bytes";
		this.tabPageUnknown.UseVisualStyleBackColor = true;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(622, 457);
		base.Controls.Add(this.tabControl1);
		base.Controls.Add(this.listBoxResources);
		base.Name = "FormResources";
		base.ShowIcon = false;
		this.Text = "Resoure Editor";
		base.Load += new System.EventHandler(FormResources_Load);
		base.KeyDown += new System.Windows.Forms.KeyEventHandler(FormResources_KeyDown);
		this.tabControl1.ResumeLayout(false);
		this.tabPage1.ResumeLayout(false);
		this.tableLayoutPanel1.ResumeLayout(false);
		this.tableLayoutPanel1.PerformLayout();
		this.tabPage2.ResumeLayout(false);
		this.tabPage2.PerformLayout();
		// 初始化图标按钮
		this.btnIcon = new Button();
		this.btnIcon.Width = 32;
		this.btnIcon.Height = 32;
		this.btnIcon.ImageAlign = ContentAlignment.MiddleCenter;
		this.btnIcon.FlatStyle = FlatStyle.Flat;
		this.btnIcon.Location = new System.Drawing.Point(224, 239);
		this.btnIcon.Name = "btnIcon";
		this.btnIcon.TabIndex = 32;
		this.btnIcon.BackColor = Color.Transparent;
		this.btnIcon.FlatAppearance.BorderSize = 1;
		this.btnIcon.FlatAppearance.BorderColor = Color.Gray;
		this.tableLayoutPanel1.Controls.Add(this.btnIcon);
		base.ResumeLayout(false);
	}
}
